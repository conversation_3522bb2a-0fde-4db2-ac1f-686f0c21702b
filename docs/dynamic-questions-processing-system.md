# Sistema di Templating e Regole di Visibilità per Questions

## Panoramica

Il sistema implementato permette di processare le questions del prodotto applicando due funzionalità principali:

1. **Templating**: Sostituzione di placeholder dinamici con valori dall'OrderEntity
2. **Regole di Visibilità**: Filtraggio delle questions basato su regole JSON

## Architettura

### Servizi Implementati

- **QuestionTemplatingService**: Gestisce la sostituzione dei placeholder
- **QuestionVisibilityService**: Valuta le regole di visibilità
- **QuestionProcessorService**: Coordina entrambe le funzionalità

### Integrazione

Il sistema è integrato in `OrderResource.java` nei seguenti endpoint:
- `GET /v3/order/{id}`
- `GET /v3/order/unchecked/{id}`
- `GET /v3/code/{orderCode}`
- `GET /v3/order/unchecked/code/{orderCode}`

## Sistema di Templating

### Formato Placeholder

I placeholder seguono il formato `{{campo.sottocampo}}`:

```
{{order.orderCode}}
{{orderItem.policyNumber}}
{{insuredItem.value}}
{{insuredItem.company.scoreESG}}
```

### Sorgenti Dati Supportate

#### 1. Order (OrderEntity)
- `{{order.id}}` - ID dell'ordine
- `{{order.orderCode}}` - Codice ordine
- `{{order.policyCode}}` - Codice polizza
- `{{order.productId}}` - ID prodotto
- `{{order.insurancePremium}}` - Premio assicurativo
- `{{order.productType}}` - Tipo prodotto

#### 2. OrderItem (OrderItemEntity)
- `{{orderItem.price}}` - Prezzo
- `{{orderItem.annualPrice}}` - Prezzo annuale
- `{{orderItem.policyNumber}}` - Numero polizza
- `{{orderItem.quantity}}` - Quantità
- `{{orderItem.state}}` - Stato

#### 3. InsuredItem (JsonNode)
- `{{insuredItem.value}}` - Valore assicurato
- `{{insuredItem.insuredAge}}` - Età assicurato
- `{{insuredItem.company.scoreESG}}` - Score ESG compagnia
- `{{insuredItem.company.name}}` - Nome compagnia
- Qualsiasi campo presente nel JSON `insured_item`

#### 4. ChosenWarranties (JsonNode) - **NUOVO**
- `{{chosenWarranties.warranties}}` - Tutte le garanzie scelte
- `{{chosenWarranties.warranties[mandatory=true]}}` - Solo garanzie obbligatorie
- `{{chosenWarranties.warranties[mandatory=false]}}` - Solo garanzie aggiuntive
- `{{chosenWarranties.warranties[*]}}` - Tutte le garanzie (equivalente al primo)
- `{{chosenWarranties.warranties[property=value]}}` - Filtraggio per qualsiasi proprietà

### Esempi di Templating

```json
{
  "content": "La sua polizza {{orderItem.policyNumber}} per l'immobile di valore {{insuredItem.value}} EUR è attiva."
}
```

Risultato:
```
"La sua polizza POL-123 per l'immobile di valore 250000 EUR è attiva."
```

## Sistema di Processing Array - **NUOVO**

### Supporto per Array di Oggetti

Il sistema ora supporta il processing di array complessi con filtraggio condizionale e formattazione intelligente.

### Formato Array Placeholder

```
{{chosenWarranties.warranties}}                    // Tutte le garanzie
{{chosenWarranties.warranties[mandatory=true]}}    // Solo garanzie obbligatorie
{{chosenWarranties.warranties[mandatory=false]}}   // Solo garanzie aggiuntive
{{chosenWarranties.warranties[category=premium]}}  // Filtraggio per categoria
{{chosenWarranties.warranties[priority=1]}}        // Filtraggio numerico
{{chosenWarranties.warranties[*]}}                 // Wildcard (tutte)
```

### Struttura Dati Supportata

```json
{
  "chosenWarranties": {
    "warranties": [
      {
        "name": "Fabbricato",
        "mandatory": true,
        "category": "base",
        "priority": 1
      },
      {
        "name": "Contenuto",
        "mandatory": true,
        "category": "base",
        "priority": 2
      },
      {
        "name": "Responsabilità Civile",
        "mandatory": false,
        "category": "optional",
        "priority": 3
      }
    ]
  }
}
```

### Formattazione Automatica

Il sistema estrae automaticamente la proprietà `name` da ogni oggetto e formatta la lista in italiano:

- **1 elemento**: "Fabbricato"
- **2 elementi**: "Fabbricato e Contenuto"
- **3+ elementi**: "Fabbricato, Contenuto e Responsabilità Civile"

### Operatori di Filtraggio

- **Stringhe**: Confronto esatto (case-sensitive)
- **Boolean**: Supporta `true`/`false` (case-insensitive)
- **Numeri**: Confronto numerico automatico
- **Wildcard**: `[*]` include tutti gli elementi

### Esempi Pratici Array

#### Esempio 1: Tutte le garanzie
```json
{
  "content": "Il pacchetto include: {{chosenWarranties.warranties}}."
}
```
Output: `"Il pacchetto include: Fabbricato, Contenuto e Responsabilità Civile."`

#### Esempio 2: Solo garanzie obbligatorie
```json
{
  "content": "Le garanzie base sono: {{chosenWarranties.warranties[mandatory=true]}}."
}
```
Output: `"Le garanzie base sono: Fabbricato e Contenuto."`

#### Esempio 3: Filtraggio per categoria
```json
{
  "content": "Garanzie premium: {{chosenWarranties.warranties[category=premium]}}."
}
```

#### Esempio 4: Scenario complesso
```json
{
  "content": "Ordine {{order.orderCode}}: garanzie base {{chosenWarranties.warranties[mandatory=true]}} e opzioni {{chosenWarranties.warranties[mandatory=false]}}."
}
```

### Gestione Errori Array

- **Array vuoto**: Il placeholder viene mantenuto invariato
- **Proprietà `name` mancante**: L'elemento viene saltato
- **Filtro non valido**: Il placeholder viene mantenuto invariato
- **Sintassi errata**: Il placeholder viene mantenuto invariato
- **Proprietà filtro inesistente**: Nessun elemento viene incluso

## Sistema di Regole di Visibilità

### Formato Regole JSON

Le regole sono memorizzate nel campo `rule` di ogni question:

```json
{
  "rule": {
    "exclusionRules": [
      {
        "field": "company.scoreESG",
        "value": "3",
        "operator": "gt"
      }
    ],
    "inclusionRules": [
      {
        "field": "tipologiaUsoAbitazione",
        "value": "S",
        "operator": "eq"
      }
    ],
    "logicOperator": "AND"
  }
}
```

### Operatori Supportati

#### Operatori Base
- `eq` - Uguale
- `ne` - Diverso
- `gt` - Maggiore
- `lt` - Minore
- `gte` - Maggiore o uguale
- `lte` - Minore o uguale
- `contains` - Contiene (per stringhe)

#### Operatori Avanzati - **NUOVO**
- `count` - Conteggio elementi in array con filtri opzionali
- `in` - Verifica se valore è contenuto in una lista
- `contains` - Verifica presenza elemento in array (per `chosenWarranties`)

### Logica di Valutazione

1. **Exclusion Rules**: Se una regola di esclusione è soddisfatta, la question viene nascosta
2. **Inclusion Rules**: Se presenti, almeno una deve essere soddisfatta (OR) o tutte (AND)
3. **Logic Operator**: Determina la logica tra inclusion rules (`AND` o `OR`)

### Esempi di Regole

#### Nascondere per compagnie con alto ESG
```json
{
  "exclusionRules": [
    {
      "field": "company.scoreESG",
      "value": "3",
      "operator": "gt"
    }
  ]
}
```

#### Mostrare solo per seconde case
```json
{
  "inclusionRules": [
    {
      "field": "tipologiaUsoAbitazione",
      "value": "S",
      "operator": "eq"
    }
  ]
}
```

#### Regole complesse con AND
```json
{
  "inclusionRules": [
    {
      "field": "company.scoreESG",
      "value": "4",
      "operator": "gte"
    },
    {
      "field": "tipologiaUsoAbitazione",
      "value": "S",
      "operator": "eq"
    }
  ],
  "logicOperator": "AND"
}
```

### Esempi Operatori Avanzati - **NUOVO**

#### Operatore `count` - Conteggio garanzie accessorie
```json
{
  "inclusionRules": [
    {
      "field": "chosenWarranties.warranties[mandatory=false]",
      "operator": "count",
      "value": "0",
      "comparison": "gt"
    }
  ]
}
```
*Mostra la question solo se ci sono garanzie aggiuntive (count > 0)*

#### Operatore `in` - Valori multipli
```json
{
  "inclusionRules": [
    {
      "field": "tipologiaProprietario",
      "operator": "in",
      "value": ["P", "PL"]
    }
  ]
}
```
*Mostra la question solo se tipologiaProprietario è "P" o "PL"*

#### Operatore `contains` per array - Warranty specifica
```json
{
  "inclusionRules": [
    {
      "field": "chosenWarranties.warranties",
      "operator": "contains",
      "value": "Furto e Rapina"
    }
  ]
}
```
*Mostra la question solo se esiste la garanzia "Furto e Rapina"*

#### Combinazione operatori avanzati
```json
{
  "inclusionRules": [
    {
      "field": "chosenWarranties.warranties",
      "operator": "contains",
      "value": "Furto e Rapina"
    },
    {
      "field": "tipologiaFabbricato",
      "operator": "eq",
      "value": "VM"
    }
  ],
  "logicOperator": "AND"
}
```
*Mostra la question solo se esiste "Furto e Rapina" E tipologiaFabbricato è "VM"*

## Esempi Pratici

### Scenario 1: Question con solo templating
```json
{
  "id": 1,
  "content": "Gentile cliente, la sua polizza {{orderItem.policyNumber}} ha un premio di {{orderItem.price}} EUR.",
  "rule": null
}
```

### Scenario 2: Question con solo regole di visibilità
```json
{
  "id": 2,
  "content": "Questa domanda è specifica per le seconde case.",
  "rule": {
    "inclusionRules": [
      {
        "field": "tipologiaUsoAbitazione",
        "value": "S",
        "operator": "eq"
      }
    ]
  }
}
```

### Scenario 3: Question con entrambe le funzionalità
```json
{
  "id": 3,
  "content": "Per la sua seconda casa a {{insuredItem.city}} di valore {{insuredItem.value}} EUR, desidera una copertura aggiuntiva?",
  "rule": {
    "inclusionRules": [
      {
        "field": "tipologiaUsoAbitazione",
        "value": "S",
        "operator": "eq"
      }
    ]
  }
}
```

## Ordine di Elaborazione

1. **Filtraggio per Visibilità**: Le questions vengono filtrate in base alle regole
2. **Applicazione Templating**: I placeholder vengono sostituiti nelle questions visibili

## Gestione Errori

- **Placeholder non trovati**: Vengono mantenuti nel testo originale
- **Regole malformate**: La question viene mostrata per default
- **Campi mancanti**: Le condizioni vengono considerate non soddisfatte
- **Errori di parsing**: Vengono loggati ma non bloccano l'elaborazione

## Performance

- Il sistema è ottimizzato per non impattare i tempi di risposta
- Le regole vengono valutate in modo efficiente
- Il templating utilizza regex ottimizzate
- Logging appropriato per debugging senza overhead

## Backward Compatibility

- Questions senza regole o placeholder continuano a funzionare normalmente
- Il sistema è completamente retrocompatibile
- Non sono richieste modifiche ai dati esistenti

## Testing

Il sistema include test completi:
- Unit test per ogni servizio
- Test di integrazione per scenari complessi
- Test di edge cases e gestione errori

## Monitoraggio

Il sistema include logging dettagliato per:
- Applicazione delle regole di visibilità
- Sostituzione dei placeholder
- Errori e warning
- Performance metrics
