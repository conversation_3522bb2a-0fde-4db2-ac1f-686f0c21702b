# Sistema di Templating e Regole di Visibilità per Questions

## Panoramica

Il sistema implementato permette di processare le questions del prodotto applicando due funzionalità principali:

1. **Templating**: Sostituzione di placeholder dinamici con valori dall'OrderEntity
2. **Regole di Visibilità**: Filtraggio delle questions basato su regole JSON

## Architettura

### Servizi Implementati

- **QuestionTemplatingService**: Gestisce la sostituzione dei placeholder
- **QuestionVisibilityService**: Valuta le regole di visibilità
- **QuestionProcessorService**: Coordina entrambe le funzionalità

### Integrazione

Il sistema è integrato in `OrderResource.java` nei seguenti endpoint:
- `GET /v3/order/{id}`
- `GET /v3/order/unchecked/{id}`
- `GET /v3/code/{orderCode}`
- `GET /v3/order/unchecked/code/{orderCode}`

## Sistema di Templating

### Formato Placeholder

I placeholder seguono il formato `{{campo.sottocampo}}`:

```
{{order.orderCode}}
{{orderItem.policyNumber}}
{{insuredItem.value}}
{{insuredItem.company.scoreESG}}
```

### Sorgenti Dati Supportate

#### 1. Order (OrderEntity)
- `{{order.id}}` - ID dell'ordine
- `{{order.orderCode}}` - Codice ordine
- `{{order.policyCode}}` - Codice polizza
- `{{order.productId}}` - ID prodotto
- `{{order.insurancePremium}}` - Premio assicurativo
- `{{order.productType}}` - Tipo prodotto

#### 2. OrderItem (OrderItemEntity)
- `{{orderItem.price}}` - Prezzo
- `{{orderItem.annualPrice}}` - Prezzo annuale
- `{{orderItem.policyNumber}}` - Numero polizza
- `{{orderItem.quantity}}` - Quantità
- `{{orderItem.state}}` - Stato

#### 3. InsuredItem (JsonNode)
- `{{insuredItem.value}}` - Valore assicurato
- `{{insuredItem.insuredAge}}` - Età assicurato
- `{{insuredItem.company.scoreESG}}` - Score ESG compagnia
- `{{insuredItem.company.name}}` - Nome compagnia
- Qualsiasi campo presente nel JSON `insured_item`

### Esempi di Templating

```json
{
  "content": "La sua polizza {{orderItem.policyNumber}} per l'immobile di valore {{insuredItem.value}} EUR è attiva."
}
```

Risultato:
```
"La sua polizza POL-123 per l'immobile di valore 250000 EUR è attiva."
```

## Sistema di Regole di Visibilità

### Formato Regole JSON

Le regole sono memorizzate nel campo `rule` di ogni question:

```json
{
  "rule": {
    "exclusionRules": [
      {
        "field": "company.scoreESG",
        "value": "3",
        "operator": "gt"
      }
    ],
    "inclusionRules": [
      {
        "field": "tipologiaUsoAbitazione",
        "value": "S",
        "operator": "eq"
      }
    ],
    "logicOperator": "AND"
  }
}
```

### Operatori Supportati

- `eq` - Uguale
- `ne` - Diverso
- `gt` - Maggiore
- `lt` - Minore
- `gte` - Maggiore o uguale
- `lte` - Minore o uguale
- `contains` - Contiene

### Logica di Valutazione

1. **Exclusion Rules**: Se una regola di esclusione è soddisfatta, la question viene nascosta
2. **Inclusion Rules**: Se presenti, almeno una deve essere soddisfatta (OR) o tutte (AND)
3. **Logic Operator**: Determina la logica tra inclusion rules (`AND` o `OR`)

### Esempi di Regole

#### Nascondere per compagnie con alto ESG
```json
{
  "exclusionRules": [
    {
      "field": "company.scoreESG",
      "value": "3",
      "operator": "gt"
    }
  ]
}
```

#### Mostrare solo per seconde case
```json
{
  "inclusionRules": [
    {
      "field": "tipologiaUsoAbitazione",
      "value": "S",
      "operator": "eq"
    }
  ]
}
```

#### Regole complesse con AND
```json
{
  "inclusionRules": [
    {
      "field": "company.scoreESG",
      "value": "4",
      "operator": "gte"
    },
    {
      "field": "tipologiaUsoAbitazione",
      "value": "S",
      "operator": "eq"
    }
  ],
  "logicOperator": "AND"
}
```

## Esempi Pratici

### Scenario 1: Question con solo templating
```json
{
  "id": 1,
  "content": "Gentile cliente, la sua polizza {{orderItem.policyNumber}} ha un premio di {{orderItem.price}} EUR.",
  "rule": null
}
```

### Scenario 2: Question con solo regole di visibilità
```json
{
  "id": 2,
  "content": "Questa domanda è specifica per le seconde case.",
  "rule": {
    "inclusionRules": [
      {
        "field": "tipologiaUsoAbitazione",
        "value": "S",
        "operator": "eq"
      }
    ]
  }
}
```

### Scenario 3: Question con entrambe le funzionalità
```json
{
  "id": 3,
  "content": "Per la sua seconda casa a {{insuredItem.city}} di valore {{insuredItem.value}} EUR, desidera una copertura aggiuntiva?",
  "rule": {
    "inclusionRules": [
      {
        "field": "tipologiaUsoAbitazione",
        "value": "S",
        "operator": "eq"
      }
    ]
  }
}
```

## Ordine di Elaborazione

1. **Filtraggio per Visibilità**: Le questions vengono filtrate in base alle regole
2. **Applicazione Templating**: I placeholder vengono sostituiti nelle questions visibili

## Gestione Errori

- **Placeholder non trovati**: Vengono mantenuti nel testo originale
- **Regole malformate**: La question viene mostrata per default
- **Campi mancanti**: Le condizioni vengono considerate non soddisfatte
- **Errori di parsing**: Vengono loggati ma non bloccano l'elaborazione

## Performance

- Il sistema è ottimizzato per non impattare i tempi di risposta
- Le regole vengono valutate in modo efficiente
- Il templating utilizza regex ottimizzate
- Logging appropriato per debugging senza overhead

## Backward Compatibility

- Questions senza regole o placeholder continuano a funzionare normalmente
- Il sistema è completamente retrocompatibile
- Non sono richieste modifiche ai dati esistenti

## Testing

Il sistema include test completi:
- Unit test per ogni servizio
- Test di integrazione per scenari complessi
- Test di edge cases e gestione errori

## Monitoraggio

Il sistema include logging dettagliato per:
- Applicazione delle regole di visibilità
- Sostituzione dei placeholder
- Errori e warning
- Performance metrics
