{"examples": [{"name": "Question con solo templating", "description": "Question che utilizza solo placeholder se<PERSON> regole di visibilità", "question": {"id": 1, "content": "Gentile cliente, la sua polizza {{orderItem.policyNumber}} per l'immobile di valore {{insuredItem.value}} EUR ha un premio annuale di {{orderItem.annualPrice}} EUR. Conferma i dati?", "position": 1, "rule": null}, "expectedOutput": "Gentile cliente, la sua polizza POL-123 per l'immobile di valore 250000 EUR ha un premio annuale di 1200.00 EUR. Conferma i dati?"}, {"name": "Question nascosta per alto ESG", "description": "Question che viene nascosta se la compagnia ha un punteggio ESG superiore a 3", "question": {"id": 2, "content": "La compagnia {{insuredItem.company.name}} ha un basso rating ESG. Desidera considerare alternative più sostenibili?", "position": 2, "rule": {"exclusionRules": [{"field": "company.scoreESG", "value": "3", "operator": "gt"}]}}, "note": "Questa question non verrà mostrata se scoreESG > 3"}, {"name": "Question per seconde case", "description": "Question specifica per immobili ad uso seconda casa", "question": {"id": 3, "content": "Per la sua seconda casa a {{insuredItem.city}} ({{insuredItem.province}}), desidera attivare la copertura per periodi di non utilizzo?", "position": 3, "rule": {"inclusionRules": [{"field": "tipologiaUsoAbitazione", "value": "S", "operator": "eq"}]}}, "note": "Visibile solo se tipologiaUsoAbitazione = 'S' (seconda casa)"}, {"name": "Question per compagnie sostenibili", "description": "Question che promuove prodotti green per compagnie con alto ESG", "question": {"id": 4, "content": "{{insuredItem.company.name}} è una compagnia con rating ESG {{insuredItem.company.scoreESG}}. Desidera informazioni sui nostri prodotti sostenibili?", "position": 4, "rule": {"inclusionRules": [{"field": "company.scoreESG", "value": "4", "operator": "gte"}]}}, "note": "Visibile solo se scoreESG >= 4"}, {"name": "Question con regole complesse AND", "description": "Question con multiple condizioni che devono essere tutte soddisfatte", "question": {"id": 5, "content": "Per la sua seconda casa di valore {{insuredItem.value}} EUR con {{insuredItem.company.name}} (ESG {{insuredItem.company.scoreESG}}), offriamo sconti speciali. Interessato?", "position": 5, "rule": {"inclusionRules": [{"field": "tipologiaUsoAbitazione", "value": "S", "operator": "eq"}, {"field": "company.scoreESG", "value": "4", "operator": "gte"}, {"field": "value", "value": "200000", "operator": "gt"}], "logicOperator": "AND"}}, "note": "Visibile solo se: seconda casa AND ESG >= 4 AND valore > 200000"}, {"name": "Question con regole complesse OR", "description": "Question con multiple condizioni di cui almeno una deve essere soddisfatta", "question": {"id": 6, "content": "Offerta speciale per {{orderItem.policyNumber}}: sconto del 10% disponibile!", "position": 6, "rule": {"inclusionRules": [{"field": "customer.profession", "value": "ENGINEER", "operator": "eq"}, {"field": "customer.age", "value": "65", "operator": "lt"}, {"field": "tipologiaTitoloAbitazione", "value": "C", "operator": "eq"}], "logicOperator": "OR"}}, "note": "Visibile se: ingegnere OR età < 65 OR proprietario"}, {"name": "Question con esclusione e inclusione", "description": "Question con sia regole di esclusione che di inclusione", "question": {"id": 7, "content": "Per l'immobile a {{insuredItem.city}} di tipo {{insuredItem.propertyType}}, raccomandiamo una copertura aggiuntiva per {{insuredItem.company.name}}.", "position": 7, "rule": {"exclusionRules": [{"field": "customer.riskProfile", "value": "HIGH", "operator": "eq"}], "inclusionRules": [{"field": "propertyType", "value": "APARTMENT", "operator": "eq"}]}}, "note": "Visibile per appartamenti ma non per clienti ad alto rischio"}, {"name": "Question per età specifica", "description": "Question basata sull'età dell'assicurato", "question": {"id": 8, "content": "Considerando che l'assicurato ha {{insuredItem.insuredAge}} anni, desidera informazioni su coperture specifiche per la sua fascia d'età?", "position": 8, "rule": {"inclusionRules": [{"field": "insuredAge", "value": "30", "operator": "gte"}, {"field": "insuredAge", "value": "65", "operator": "lt"}], "logicOperator": "AND"}}, "note": "Visibile per età tra 30 e 65 anni"}, {"name": "Question per tipo costruttivo", "description": "Question specifica per il tipo di costruzione", "question": {"id": 9, "content": "Il suo immobile di tipo costruttivo {{insuredItem.tipologiaCostruttivaAbitazione}} richiede valutazioni specifiche. Procediamo?", "position": 9, "rule": {"inclusionRules": [{"field": "tipologiaCostruttivaAbitazione", "value": "A", "operator": "eq"}]}}, "note": "Visibile solo per costruzioni in muratura (tipo A)"}, {"name": "Question sempre visibile", "description": "Question che appare sempre, indipendentemente dalle condizioni", "question": {"id": 10, "content": "Ordine {{order.orderCode}} - Prodotto {{order.productType}} - Premio: {{order.insurancePremium}} EUR. Ha domande sul contratto?", "position": 10, "rule": null}, "note": "<PERSON><PERSON><PERSON> visibile, solo con templating"}], "testData": {"description": "Dati di test per verificare il comportamento delle questions", "orderEntity": {"id": 123, "orderCode": "ORD-2024-001", "policyCode": "POL-HOME-001", "productId": 456, "productType": "HOME_INSURANCE", "insurancePremium": "1250.75"}, "orderItem": {"price": "1250.75", "annualPrice": "1250.75", "policyNumber": "POL-HOME-001-ITEM", "quantity": 1}, "insuredItem": {"tipologiaUsoAbitazione": "S", "tipologiaTitoloAbitazione": "C", "tipologiaCostruttivaAbitazione": "A", "value": "350000", "insuredAge": "42", "propertyType": "APARTMENT", "city": "Milano", "province": "MI", "company": {"scoreESG": "4", "name": "Assicurazioni Integrate SpA", "code": "AIS", "rating": "A+"}, "customer": {"age": "42", "profession": "ENGINEER", "riskProfile": "LOW"}}}, "expectedResults": {"description": "Risultati attesi con i dati di test", "visibleQuestions": [1, 3, 4, 5, 6, 7, 8, 9, 10], "hiddenQuestions": [2], "reasonsForHiding": {"2": "company.scoreESG (4) > 3"}}}