# Estensione Sistema di Templating - Supporto Array e Oggetti Complessi

## Panoramica

Il sistema di templating è stato esteso per supportare la gestione di array e oggetti complessi nei placeholder, con particolare focus sui dati `chosenWarranties` che contengono array di garanzie.

## Nuove Funzionalità Implementate

### 1. Supporto per Array di Oggetti

Il sistema ora può processare array di oggetti e formattarli automaticamente come stringhe leggibili in italiano.

**Formato supportato:**
```
{{chosenWarranties.warranties}}
```

**Struttura dati:**
```json
{
  "chosenWarranties": {
    "warranties": [
      {"name": "Fabbricato", "mandatory": true},
      {"name": "Contenuto", "mandatory": true},
      {"name": "Responsabilità Civile", "mandatory": false}
    ]
  }
}
```

**Output:**
```
"Fabbricato, Contenuto e Responsabilità Civile"
```

### 2. Filtraggio Condizionale degli Array

Il sistema supporta filtri condizionali per estrarre sottoinsiemi di array basati su proprietà specifiche.

**Sintassi:**
```
{{chosenWarranties.warranties[property=value]}}
```

**Esempi:**
- `{{chosenWarranties.warranties[mandatory=true]}}` → "Fabbricato e Contenuto"
- `{{chosenWarranties.warranties[mandatory=false]}}` → "Responsabilità Civile"
- `{{chosenWarranties.warranties[category=premium]}}` → Garanzie con categoria premium
- `{{chosenWarranties.warranties[priority=1]}}` → Garanzie con priorità 1
- `{{chosenWarranties.warranties[*]}}` → Tutte le garanzie (wildcard)

### 3. Formattazione Intelligente in Italiano

Il sistema formatta automaticamente le liste secondo le convenzioni italiane:

- **1 elemento**: "Fabbricato"
- **2 elementi**: "Fabbricato e Contenuto"
- **3+ elementi**: "Fabbricato, Contenuto e Responsabilità Civile"

### 4. Supporto per Tipi di Dati Multipli

Il sistema di filtraggio supporta diversi tipi di confronto:

- **Stringhe**: Confronto esatto (case-sensitive)
- **Boolean**: `true`/`false` (case-insensitive)
- **Numeri**: Confronto numerico automatico

## Implementazione Tecnica

### Metodi Aggiunti a QuestionTemplatingService

#### `extractFromChosenWarranties(String fieldPath, OrderEntity orderEntity)`
- Punto di ingresso per il processing di chosenWarranties
- Gestisce sia accesso semplice che con filtri

#### `processArrayWithFilter(String fieldPath, JsonNode chosenWarranties)`
- Parsing della sintassi `[property=value]`
- Estrazione del nome array e dell'espressione di filtro
- Delegazione al formatting

#### `formatArrayAsString(JsonNode arrayNode, String filterProperty, String filterValue)`
- Iterazione sull'array con applicazione del filtro
- Estrazione della proprietà `name` da ogni oggetto
- Formattazione come stringa italiana

#### `matchesFilterValue(String itemValue, String filterValue)`
- Confronto intelligente per diversi tipi di dati
- Gestione di boolean, numeri e stringhe

#### `formatListAsItalianString(List<String> items)`
- Formattazione specifica per la lingua italiana
- Gestione di 1, 2 e 3+ elementi

### Gestione Errori e Edge Cases

- **Array vuoto**: Placeholder mantenuto invariato
- **Proprietà `name` mancante**: Elemento saltato
- **Filtro non valido**: Placeholder mantenuto invariato
- **Sintassi errata**: Placeholder mantenuto invariato
- **Proprietà filtro inesistente**: Nessun elemento incluso
- **chosenWarranties null**: Placeholder mantenuto invariato

## Test Implementati

### Unit Test Estesi (QuestionTemplatingServiceTest)

**Nuovi test aggiunti:**
- `testProcessTemplate_ChosenWarranties_AllWarranties()`
- `testProcessTemplate_ChosenWarranties_MandatoryOnly()`
- `testProcessTemplate_ChosenWarranties_OptionalOnly()`
- `testProcessTemplate_ChosenWarranties_WildcardFilter()`
- `testProcessTemplate_ChosenWarranties_NumericFilter()`
- `testProcessTemplate_ChosenWarranties_NoMatches()`
- `testProcessTemplate_ChosenWarranties_EmptyArray()`
- `testProcessTemplate_ChosenWarranties_NullChosenWarranties()`
- `testProcessTemplate_ChosenWarranties_MissingNameProperty()`
- `testProcessTemplate_ChosenWarranties_InvalidFilterSyntax()`
- `testProcessTemplate_ChosenWarranties_MissingFilterProperty()`
- `testProcessTemplate_ChosenWarranties_SingleItem()`
- `testProcessTemplate_ChosenWarranties_TwoItems()`
- `testProcessTemplate_ChosenWarranties_ComplexScenario()`
- `testProcessTemplate_ChosenWarranties_BooleanStringComparison()`

### Integration Test (ArrayTemplatingIntegrationTest)

**Scenari testati:**
- Processing completo con array di base
- Percorsi nidificati (`chosenWarranties.data.warranties`)
- Filtraggio complesso con multiple proprietà
- Edge cases e gestione errori
- Scenari vuoti e null
- Combinazione con templating regolare
- Modifica in-place

## Esempi Pratici

### Scenario 1: Question Base con Array
```json
{
  "content": "Confermi che il pacchetto scelto comprendente le garanzie {{chosenWarranties.warranties}} corrisponde alle tue esigenze?"
}
```
**Output:** "Confermi che il pacchetto scelto comprendente le garanzie Fabbricato, Contenuto e Responsabilità Civile corrisponde alle tue esigenze?"

### Scenario 2: Filtraggio per Garanzie Obbligatorie
```json
{
  "content": "Le garanzie base {{chosenWarranties.warranties[mandatory=true]}} sono incluse automaticamente nel pacchetto."
}
```
**Output:** "Le garanzie base Fabbricato e Contenuto sono incluse automaticamente nel pacchetto."

### Scenario 3: Scenario Complesso
```json
{
  "content": "Riepilogo ordine {{order.orderCode}}: garanzie base {{chosenWarranties.warranties[mandatory=true]}} (incluse) e garanzie aggiuntive {{chosenWarranties.warranties[mandatory=false]}} (opzionali) per un totale di {{chosenWarranties.warranties}}."
}
```
**Output:** "Riepilogo ordine ORD-2024-001: garanzie base Fabbricato e Contenuto (incluse) e garanzie aggiuntive Responsabilità Civile (opzionali) per un totale di Fabbricato, Contenuto e Responsabilità Civile."

## Backward Compatibility

- **Completamente retrocompatibile** con il sistema esistente
- **Nessuna modifica** ai placeholder esistenti richiesta
- **Funzionalità aggiuntiva** che non impatta il comportamento esistente
- **Graceful degradation** per dati mancanti o malformati

## Performance

- **Parsing efficiente** della sintassi dei filtri
- **Iterazione ottimizzata** degli array
- **Caching implicito** attraverso il processing una tantum
- **Impatto minimo** sui tempi di risposta

## Estensibilità Futura

Il sistema è progettato per essere facilmente estendibile:

- **Nuovi operatori di filtro** (contains, startsWith, etc.)
- **Supporto per array nidificati** più complessi
- **Formattazione personalizzabile** per diverse lingue
- **Aggregazioni** (count, sum, etc.)
- **Ordinamento** degli array prima del formatting

## Monitoraggio e Debugging

- **Logging dettagliato** per ogni fase del processing
- **Tracciamento** dei filtri applicati e risultati
- **Gestione esplicita** degli errori con log appropriati
- **Metriche** per performance monitoring

## Conclusioni

L'estensione implementata fornisce un sistema robusto e flessibile per il processing di array complessi mantenendo la semplicità d'uso e la backward compatibility. Il sistema è pronto per l'uso in produzione e può gestire scenari complessi di business con garanzie e prodotti assicurativi.
