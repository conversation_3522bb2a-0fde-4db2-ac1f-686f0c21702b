package it.yolo.service.question;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.response.client.product.Question;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class QuestionVisibilityServiceTest {

    private QuestionVisibilityService visibilityService;
    private ObjectMapper objectMapper;
    private OrderEntity orderEntity;
    private OrderItemEntity orderItemEntity;

    @BeforeEach
    void setUp() {
        visibilityService = new QuestionVisibilityService();
        objectMapper = new ObjectMapper();
        
        // Setup test data
        orderEntity = new OrderEntity();
        orderItemEntity = new OrderItemEntity();

        // Setup insured_item with test data
        ObjectNode insuredItem = objectMapper.createObjectNode();
        insuredItem.put("tipologiaUsoAbitazione", "S");
        insuredItem.put("tipologiaTitoloAbitazione", "C");
        insuredItem.put("value", "250000");
        insuredItem.put("insuredAge", "35");
        
        // Add nested company object with scoreESG field
        ObjectNode company = objectMapper.createObjectNode();
        company.put("scoreESG", "4"); // Default value is 4 (greater than 3)
        company.put("name", "Test Company");
        insuredItem.set("company", company);

        orderItemEntity.setInsured_item(insuredItem);

        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItemEntity);
        orderEntity.setOrderItem(orderItems);
    }

    @Test
    void testIsQuestionVisible_NoRules() {
        Question question = new Question();
        question.setId(1);
        question.setContent("Test question");
        question.setRule(null);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question without rules should be visible");
    }

    @Test
    void testIsQuestionVisible_NullQuestion() {
        boolean result = visibilityService.isQuestionVisible(null, orderEntity);
        assertFalse(result, "Null question should not be visible");
    }

    @Test
    void testIsQuestionVisible_NullOrderEntity() {
        Question question = new Question();
        question.setId(1);
        question.setContent("Test question");

        boolean result = visibilityService.isQuestionVisible(question, null);
        assertTrue(result, "Question should be visible by default when order entity is null");
    }

    @Test
    void testIsQuestionVisible_ExclusionRule_Satisfied() {
        Question question = new Question();
        question.setId(1);
        question.setContent("Test question");

        // Create exclusion rule: hide if company.scoreESG > 3
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode exclusionRules = objectMapper.createArrayNode();
        ObjectNode exclusionRule = objectMapper.createObjectNode();
        exclusionRule.put("field", "company.scoreESG");
        exclusionRule.put("value", "3");
        exclusionRule.put("operator", "gt");
        exclusionRules.add(exclusionRule);
        rule.set("exclusionRules", exclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertFalse(result, "Question should be hidden when exclusion rule is satisfied");
    }

    @Test
    void testIsQuestionVisible_ExclusionRule_NotSatisfied() {
        Question question = new Question();
        question.setId(1);
        question.setContent("Test question");

        // Create exclusion rule: hide if company.scoreESG > 5
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode exclusionRules = objectMapper.createArrayNode();
        ObjectNode exclusionRule = objectMapper.createObjectNode();
        exclusionRule.put("field", "company.scoreESG");
        exclusionRule.put("value", "5");
        exclusionRule.put("operator", "gt");
        exclusionRules.add(exclusionRule);
        rule.set("exclusionRules", exclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible when exclusion rule is not satisfied");
    }

    @Test
    void testIsQuestionVisible_InclusionRule_AND_AllSatisfied() {
        Question question = new Question();
        question.setId(1);
        question.setContent("Test question");

        // Create inclusion rules with AND logic: show if scoreESG >= 4 AND tipologiaUsoAbitazione = "S"
        ObjectNode rule = objectMapper.createObjectNode();
        rule.put("logicOperator", "AND");
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        
        ObjectNode rule1 = objectMapper.createObjectNode();
        rule1.put("field", "company.scoreESG");
        rule1.put("value", "4");
        rule1.put("operator", "gte");
        inclusionRules.add(rule1);
        
        ObjectNode rule2 = objectMapper.createObjectNode();
        rule2.put("field", "tipologiaUsoAbitazione");
        rule2.put("value", "S");
        rule2.put("operator", "eq");
        inclusionRules.add(rule2);
        
        rule.set("inclusionRules", inclusionRules);
        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible when all AND inclusion rules are satisfied");
    }

    @Test
    void testIsQuestionVisible_InclusionRule_AND_OneFails() {
        Question question = new Question();
        question.setId(1);
        question.setContent("Test question");

        // Create inclusion rules with AND logic: show if scoreESG >= 5 AND tipologiaUsoAbitazione = "S"
        ObjectNode rule = objectMapper.createObjectNode();
        rule.put("logicOperator", "AND");
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        
        ObjectNode rule1 = objectMapper.createObjectNode();
        rule1.put("field", "company.scoreESG");
        rule1.put("value", "5");
        rule1.put("operator", "gte");
        inclusionRules.add(rule1);
        
        ObjectNode rule2 = objectMapper.createObjectNode();
        rule2.put("field", "tipologiaUsoAbitazione");
        rule2.put("value", "S");
        rule2.put("operator", "eq");
        inclusionRules.add(rule2);
        
        rule.set("inclusionRules", inclusionRules);
        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertFalse(result, "Question should be hidden when one AND inclusion rule fails");
    }

    @Test
    void testIsQuestionVisible_InclusionRule_OR_OneSatisfied() {
        Question question = new Question();
        question.setId(1);
        question.setContent("Test question");

        // Create inclusion rules with OR logic: show if scoreESG >= 5 OR tipologiaUsoAbitazione = "S"
        ObjectNode rule = objectMapper.createObjectNode();
        rule.put("logicOperator", "OR");
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        
        ObjectNode rule1 = objectMapper.createObjectNode();
        rule1.put("field", "company.scoreESG");
        rule1.put("value", "5");
        rule1.put("operator", "gte");
        inclusionRules.add(rule1);
        
        ObjectNode rule2 = objectMapper.createObjectNode();
        rule2.put("field", "tipologiaUsoAbitazione");
        rule2.put("value", "S");
        rule2.put("operator", "eq");
        inclusionRules.add(rule2);
        
        rule.set("inclusionRules", inclusionRules);
        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible when at least one OR inclusion rule is satisfied");
    }

    @Test
    void testIsQuestionVisible_InclusionRule_OR_NoneSatisfied() {
        Question question = new Question();
        question.setId(1);
        question.setContent("Test question");

        // Create inclusion rules with OR logic: show if scoreESG >= 5 OR tipologiaUsoAbitazione = "X"
        ObjectNode rule = objectMapper.createObjectNode();
        rule.put("logicOperator", "OR");
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        
        ObjectNode rule1 = objectMapper.createObjectNode();
        rule1.put("field", "company.scoreESG");
        rule1.put("value", "5");
        rule1.put("operator", "gte");
        inclusionRules.add(rule1);
        
        ObjectNode rule2 = objectMapper.createObjectNode();
        rule2.put("field", "tipologiaUsoAbitazione");
        rule2.put("value", "X");
        rule2.put("operator", "eq");
        inclusionRules.add(rule2);
        
        rule.set("inclusionRules", inclusionRules);
        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertFalse(result, "Question should be hidden when no OR inclusion rules are satisfied");
    }

    @Test
    void testIsQuestionVisible_MultipleOperators() {
        Question question = new Question();
        question.setId(1);
        question.setContent("Test question");

        // Test different operators: eq, ne, lt, lte, gt, gte
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        
        // Test eq operator
        ObjectNode eqRule = objectMapper.createObjectNode();
        eqRule.put("field", "tipologiaUsoAbitazione");
        eqRule.put("value", "S");
        eqRule.put("operator", "eq");
        inclusionRules.add(eqRule);
        
        rule.set("inclusionRules", inclusionRules);
        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible with eq operator");
    }

    @Test
    void testIsQuestionVisible_NoOrderItems() {
        orderEntity.setOrderItem(new ArrayList<>());
        
        Question question = new Question();
        question.setId(1);
        question.setContent("Test question");

        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode exclusionRules = objectMapper.createArrayNode();
        ObjectNode exclusionRule = objectMapper.createObjectNode();
        exclusionRule.put("field", "company.scoreESG");
        exclusionRule.put("value", "3");
        exclusionRule.put("operator", "gt");
        exclusionRules.add(exclusionRule);
        rule.set("exclusionRules", exclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible by default when no order items available");
    }
}
