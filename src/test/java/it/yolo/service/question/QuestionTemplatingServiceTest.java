package it.yolo.service.question;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class QuestionTemplatingServiceTest {

    private QuestionTemplatingService templatingService;
    private ObjectMapper objectMapper;
    private OrderEntity orderEntity;
    private OrderItemEntity orderItemEntity;

    @BeforeEach
    void setUp() {
        templatingService = new QuestionTemplatingService();
        objectMapper = new ObjectMapper();
        
        // Setup test data
        orderEntity = new OrderEntity();
        orderEntity.setId(123L);
        orderEntity.setOrderCode("ORD-001");
        orderEntity.setPolicyCode("POL-001");
        orderEntity.setProductId(456);
        orderEntity.setBrokerId(789);
        orderEntity.setCompanyId(101);
        orderEntity.setCustomerId(202);
        orderEntity.setInsurancePremium(new BigDecimal("150.50"));
        orderEntity.setCreatedBy("test-user");
        orderEntity.setUpdatedBy("test-user");
        orderEntity.setProductType("INSURANCE");

        orderItemEntity = new OrderItemEntity();
        orderItemEntity.setPrice("100.00");
        orderItemEntity.setAnnualPrice("1200.00");
        orderItemEntity.setProduct_id(456);
        orderItemEntity.setPolicy_number("POL-123");
        orderItemEntity.setMaster_policy_number("MPOL-123");
        orderItemEntity.setExternal_id("EXT-123");
        orderItemEntity.setState("ACTIVE");
        orderItemEntity.setQuantity(1);
        orderItemEntity.setPacketId(789);

        // Setup insured_item with nested data
        ObjectNode insuredItem = objectMapper.createObjectNode();
        insuredItem.put("tipologiaUsoAbitazione", "S");
        insuredItem.put("tipologiaTitoloAbitazione", "C");
        insuredItem.put("value", "250000");
        insuredItem.put("insuredAge", "35");
        
        // Add nested company object
        ObjectNode company = objectMapper.createObjectNode();
        company.put("scoreESG", "4");
        company.put("name", "Test Company");
        insuredItem.set("company", company);

        orderItemEntity.setInsured_item(insuredItem);

        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItemEntity);
        orderEntity.setOrderItem(orderItems);
    }

    @Test
    void testProcessTemplate_NoPlaceholders() {
        String content = "This is a simple question without placeholders.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals(content, result);
    }

    @Test
    void testProcessTemplate_OrderFields() {
        String content = "Your order {{order.orderCode}} for product {{order.productId}} has premium {{order.insurancePremium}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Your order ORD-001 for product 456 has premium 150.50.", result);
    }

    @Test
    void testProcessTemplate_OrderItemFields() {
        String content = "Policy {{orderItem.policyNumber}} costs {{orderItem.price}} with quantity {{orderItem.quantity}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Policy POL-123 costs 100.00 with quantity 1.", result);
    }

    @Test
    void testProcessTemplate_InsuredItemFields() {
        String content = "Property value is {{insuredItem.value}} and insured age is {{insuredItem.insuredAge}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Property value is 250000 and insured age is 35.", result);
    }

    @Test
    void testProcessTemplate_NestedInsuredItemFields() {
        String content = "Company {{insuredItem.company.name}} has ESG score {{insuredItem.company.scoreESG}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Company Test Company has ESG score 4.", result);
    }

    @Test
    void testProcessTemplate_MixedPlaceholders() {
        String content = "Order {{order.orderCode}} for {{insuredItem.company.name}} with value {{insuredItem.value}} costs {{orderItem.price}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Order ORD-001 for Test Company with value 250000 costs 100.00.", result);
    }

    @Test
    void testProcessTemplate_UnknownPlaceholder() {
        String content = "Unknown field: {{order.unknownField}} and {{insuredItem.unknownField}}.";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Unknown field: {{order.unknownField}} and {{insuredItem.unknownField}}.", result);
    }

    @Test
    void testProcessTemplate_NullContent() {
        String result = templatingService.processTemplate(null, orderEntity);
        assertNull(result);
    }

    @Test
    void testProcessTemplate_EmptyContent() {
        String result = templatingService.processTemplate("", orderEntity);
        assertEquals("", result);
    }

    @Test
    void testProcessTemplate_NullOrderEntity() {
        String content = "Order {{order.orderCode}} test.";
        String result = templatingService.processTemplate(content, null);
        assertEquals(content, result);
    }

    @Test
    void testProcessTemplate_NoOrderItems() {
        orderEntity.setOrderItem(new ArrayList<>());
        String content = "Price: {{orderItem.price}}";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Price: {{orderItem.price}}", result);
    }

    @Test
    void testProcessTemplate_NullInsuredItem() {
        orderItemEntity.setInsured_item(null);
        String content = "Value: {{insuredItem.value}}";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Value: {{insuredItem.value}}", result);
    }

    @Test
    void testProcessTemplate_MultipleSamePlaceholders() {
        String content = "Order {{order.orderCode}} - Code: {{order.orderCode}}";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Order ORD-001 - Code: ORD-001", result);
    }

    @Test
    void testProcessTemplate_ComplexScenario() {
        String content = "Gentile cliente, la sua polizza {{orderItem.policyNumber}} " +
                        "per l'immobile di valore {{insuredItem.value}} EUR " +
                        "della compagnia {{insuredItem.company.name}} " +
                        "ha un premio annuale di {{orderItem.annualPrice}} EUR. " +
                        "L'età dell'assicurato è {{insuredItem.insuredAge}} anni.";
        
        String expected = "Gentile cliente, la sua polizza POL-123 " +
                         "per l'immobile di valore 250000 EUR " +
                         "della compagnia Test Company " +
                         "ha un premio annuale di 1200.00 EUR. " +
                         "L'età dell'assicurato è 35 anni.";
        
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals(expected, result);
    }

    @Test
    void testProcessTemplate_InvalidPlaceholderFormat() {
        String content = "Invalid: {order.orderCode} and {{order.orderCode} and {order.orderCode}}";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Invalid: {order.orderCode} and {{order.orderCode} and {order.orderCode}}", result);
    }

    @Test
    void testProcessTemplate_EmptyPlaceholder() {
        String content = "Empty placeholder: {{}}";
        String result = templatingService.processTemplate(content, orderEntity);
        assertEquals("Empty placeholder: {{}}", result);
    }
}
