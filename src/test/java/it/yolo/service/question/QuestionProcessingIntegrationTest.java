package it.yolo.service.question;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.response.client.product.Question;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for the complete question processing system.
 * Tests the interaction between QuestionTemplatingService, QuestionVisibilityService,
 * and QuestionProcessorService.
 */
class QuestionProcessingIntegrationTest {

    private QuestionProcessorService processorService;
    private QuestionTemplatingService templatingService;
    private QuestionVisibilityService visibilityService;
    private ObjectMapper objectMapper;
    private OrderEntity orderEntity;

    @BeforeEach
    void setUp() {
        // Create real instances (not mocks) for integration testing
        templatingService = new QuestionTemplatingService();
        visibilityService = new QuestionVisibilityService();
        processorService = new QuestionProcessorService();
        
        // Manually inject dependencies (since we're not using CDI in tests)
        processorService.templatingService = templatingService;
        processorService.visibilityService = visibilityService;
        
        objectMapper = new ObjectMapper();
        
        // Setup comprehensive test data
        setupOrderEntity();
    }

    private void setupOrderEntity() {
        orderEntity = new OrderEntity();
        orderEntity.setId(123L);
        orderEntity.setOrderCode("ORD-2024-001");
        orderEntity.setPolicyCode("POL-HOME-001");
        orderEntity.setProductId(456);
        orderEntity.setBrokerId(789);
        orderEntity.setCompanyId(101);
        orderEntity.setCustomerId(202);
        orderEntity.setInsurancePremium(new BigDecimal("1250.75"));
        orderEntity.setCreatedBy("integration-test");
        orderEntity.setUpdatedBy("integration-test");
        orderEntity.setProductType("HOME_INSURANCE");

        OrderItemEntity orderItemEntity = new OrderItemEntity();
        orderItemEntity.setPrice("1250.75");
        orderItemEntity.setAnnualPrice("1250.75");
        orderItemEntity.setProduct_id(456);
        orderItemEntity.setPolicy_number("POL-HOME-001-ITEM");
        orderItemEntity.setMaster_policy_number("MPOL-HOME-001");
        orderItemEntity.setExternal_id("EXT-HOME-001");
        orderItemEntity.setState("ACTIVE");
        orderItemEntity.setQuantity(1);
        orderItemEntity.setPacketId(789);

        // Setup comprehensive insured_item data
        ObjectNode insuredItem = objectMapper.createObjectNode();
        insuredItem.put("tipologiaUsoAbitazione", "S"); // Seconda casa
        insuredItem.put("tipologiaTitoloAbitazione", "C"); // Proprietà
        insuredItem.put("tipologiaCostruttivaAbitazione", "A"); // Muratura
        insuredItem.put("value", "350000");
        insuredItem.put("insuredAge", "42");
        insuredItem.put("propertyType", "APARTMENT");
        insuredItem.put("city", "Milano");
        insuredItem.put("province", "MI");
        
        // Add nested company object with comprehensive data
        ObjectNode company = objectMapper.createObjectNode();
        company.put("scoreESG", "4");
        company.put("name", "Assicurazioni Integrate SpA");
        company.put("code", "AIS");
        company.put("rating", "A+");
        insuredItem.set("company", company);

        // Add nested customer data
        ObjectNode customer = objectMapper.createObjectNode();
        customer.put("age", "42");
        customer.put("profession", "ENGINEER");
        customer.put("riskProfile", "LOW");
        insuredItem.set("customer", customer);

        orderItemEntity.setInsured_item(insuredItem);

        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItemEntity);
        orderEntity.setOrderItem(orderItems);
    }

    @Test
    void testCompleteProcessing_TemplatingOnly() {
        List<Question> questions = createQuestionsWithTemplatingOnly();
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(3, result.size());
        
        // Verify templating was applied correctly
        assertEquals("La sua polizza POL-HOME-001-ITEM per l'immobile di valore 350000 EUR è attiva.", 
                    result.get(0).getContent());
        assertEquals("Gentile cliente, il suo ordine ORD-2024-001 ha un premio di 1250.75 EUR.", 
                    result.get(1).getContent());
        assertEquals("L'assicurato di 42 anni risiede a Milano (MI).", 
                    result.get(2).getContent());
    }

    @Test
    void testCompleteProcessing_VisibilityOnly() {
        List<Question> questions = createQuestionsWithVisibilityRules();
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        // Question 1: should be visible (no rules)
        // Question 2: should be hidden (scoreESG > 3, and our test data has scoreESG = 4)
        // Question 3: should be visible (tipologiaUsoAbitazione = "S")
        assertEquals(2, result.size());
        assertEquals("Domanda sempre visibile", result.get(0).getContent());
        assertEquals("Domanda per seconde case", result.get(1).getContent());
    }

    @Test
    void testCompleteProcessing_TemplatingAndVisibility() {
        List<Question> questions = createQuestionsWithBothFeatures();
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        // Question 1: visible + templated
        // Question 2: hidden due to visibility rule
        // Question 3: visible + templated
        assertEquals(2, result.size());
        
        assertEquals("Benvenuto! La sua polizza POL-HOME-001-ITEM per Assicurazioni Integrate SpA è pronta.", 
                    result.get(0).getContent());
        assertEquals("Per l'immobile di valore 350000 EUR a Milano, conferma i dati?", 
                    result.get(1).getContent());
    }

    @Test
    void testCompleteProcessing_InPlace() {
        List<Question> questions = createQuestionsWithBothFeatures();
        int originalSize = questions.size();
        
        processorService.processQuestionsInPlace(questions, orderEntity);
        
        // Should have 2 questions remaining (one was filtered out)
        assertEquals(2, questions.size());
        assertTrue(questions.size() < originalSize);
        
        // Verify content was processed
        assertEquals("Benvenuto! La sua polizza POL-HOME-001-ITEM per Assicurazioni Integrate SpA è pronta.", 
                    questions.get(0).getContent());
        assertEquals("Per l'immobile di valore 350000 EUR a Milano, conferma i dati?", 
                    questions.get(1).getContent());
    }

    @Test
    void testCompleteProcessing_ComplexScenario() {
        List<Question> questions = createComplexScenario();
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        // Verify complex business logic
        assertEquals(3, result.size());
        
        // Question 1: Always visible, with templating
        assertTrue(result.get(0).getContent().contains("ORD-2024-001"));
        assertTrue(result.get(0).getContent().contains("1250.75"));
        
        // Question 2: Visible only for seconda casa (tipologiaUsoAbitazione = "S")
        assertTrue(result.get(1).getContent().contains("seconda casa"));
        
        // Question 3: Visible for high ESG score companies (scoreESG >= 4)
        assertTrue(result.get(2).getContent().contains("Assicurazioni Integrate SpA"));
        assertTrue(result.get(2).getContent().contains("sostenibile"));
    }

    @Test
    void testEdgeCases() {
        List<Question> questions = new ArrayList<>();
        
        // Question with empty content
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("");
        questions.add(q1);
        
        // Question with null content
        Question q2 = new Question();
        q2.setId(2);
        q2.setContent(null);
        questions.add(q2);
        
        // Question with malformed placeholder
        Question q3 = new Question();
        q3.setId(3);
        q3.setContent("Malformed: {order.orderCode} and {{incomplete");
        questions.add(q3);
        
        List<Question> result = processorService.processQuestions(questions, orderEntity);
        
        assertEquals(3, result.size());
        assertEquals("", result.get(0).getContent());
        assertNull(result.get(1).getContent());
        assertEquals("Malformed: {order.orderCode} and {{incomplete", result.get(2).getContent());
    }

    private List<Question> createQuestionsWithTemplatingOnly() {
        List<Question> questions = new ArrayList<>();
        
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("La sua polizza {{orderItem.policyNumber}} per l'immobile di valore {{insuredItem.value}} EUR è attiva.");
        questions.add(q1);
        
        Question q2 = new Question();
        q2.setId(2);
        q2.setContent("Gentile cliente, il suo ordine {{order.orderCode}} ha un premio di {{order.insurancePremium}} EUR.");
        questions.add(q2);
        
        Question q3 = new Question();
        q3.setId(3);
        q3.setContent("L'assicurato di {{insuredItem.insuredAge}} anni risiede a {{insuredItem.city}} ({{insuredItem.province}}).");
        questions.add(q3);
        
        return questions;
    }

    private List<Question> createQuestionsWithVisibilityRules() {
        List<Question> questions = new ArrayList<>();
        
        // Question 1: Always visible (no rules)
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("Domanda sempre visibile");
        questions.add(q1);
        
        // Question 2: Hidden if scoreESG > 3
        Question q2 = new Question();
        q2.setId(2);
        q2.setContent("Domanda per compagnie con basso ESG");
        ObjectNode rule2 = objectMapper.createObjectNode();
        ArrayNode exclusionRules2 = objectMapper.createArrayNode();
        ObjectNode exclusionRule2 = objectMapper.createObjectNode();
        exclusionRule2.put("field", "company.scoreESG");
        exclusionRule2.put("value", "3");
        exclusionRule2.put("operator", "gt");
        exclusionRules2.add(exclusionRule2);
        rule2.set("exclusionRules", exclusionRules2);
        q2.setRule(rule2);
        questions.add(q2);
        
        // Question 3: Visible only for seconda casa
        Question q3 = new Question();
        q3.setId(3);
        q3.setContent("Domanda per seconde case");
        ObjectNode rule3 = objectMapper.createObjectNode();
        ArrayNode inclusionRules3 = objectMapper.createArrayNode();
        ObjectNode inclusionRule3 = objectMapper.createObjectNode();
        inclusionRule3.put("field", "tipologiaUsoAbitazione");
        inclusionRule3.put("value", "S");
        inclusionRule3.put("operator", "eq");
        inclusionRules3.add(inclusionRule3);
        rule3.set("inclusionRules", inclusionRules3);
        q3.setRule(rule3);
        questions.add(q3);
        
        return questions;
    }

    private List<Question> createQuestionsWithBothFeatures() {
        List<Question> questions = new ArrayList<>();
        
        // Question 1: Always visible with templating
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("Benvenuto! La sua polizza {{orderItem.policyNumber}} per {{insuredItem.company.name}} è pronta.");
        questions.add(q1);
        
        // Question 2: Hidden + templating (should not appear in result)
        Question q2 = new Question();
        q2.setId(2);
        q2.setContent("Domanda nascosta per {{insuredItem.company.name}} con ESG {{insuredItem.company.scoreESG}}");
        ObjectNode rule2 = objectMapper.createObjectNode();
        ArrayNode exclusionRules2 = objectMapper.createArrayNode();
        ObjectNode exclusionRule2 = objectMapper.createObjectNode();
        exclusionRule2.put("field", "company.scoreESG");
        exclusionRule2.put("value", "3");
        exclusionRule2.put("operator", "gt");
        exclusionRules2.add(exclusionRule2);
        rule2.set("exclusionRules", exclusionRules2);
        q2.setRule(rule2);
        questions.add(q2);
        
        // Question 3: Visible for seconda casa + templating
        Question q3 = new Question();
        q3.setId(3);
        q3.setContent("Per l'immobile di valore {{insuredItem.value}} EUR a {{insuredItem.city}}, conferma i dati?");
        ObjectNode rule3 = objectMapper.createObjectNode();
        ArrayNode inclusionRules3 = objectMapper.createArrayNode();
        ObjectNode inclusionRule3 = objectMapper.createObjectNode();
        inclusionRule3.put("field", "tipologiaUsoAbitazione");
        inclusionRule3.put("value", "S");
        inclusionRule3.put("operator", "eq");
        inclusionRules3.add(inclusionRule3);
        rule3.set("inclusionRules", inclusionRules3);
        q3.setRule(rule3);
        questions.add(q3);
        
        return questions;
    }

    private List<Question> createComplexScenario() {
        List<Question> questions = new ArrayList<>();
        
        // Question 1: Always visible with complex templating
        Question q1 = new Question();
        q1.setId(1);
        q1.setContent("Ordine {{order.orderCode}} - Premio: {{order.insurancePremium}} EUR - Prodotto: {{order.productType}}");
        questions.add(q1);
        
        // Question 2: Visible only for seconda casa
        Question q2 = new Question();
        q2.setId(2);
        q2.setContent("Questa domanda è specifica per la sua seconda casa a {{insuredItem.city}}.");
        ObjectNode rule2 = objectMapper.createObjectNode();
        ArrayNode inclusionRules2 = objectMapper.createArrayNode();
        ObjectNode inclusionRule2 = objectMapper.createObjectNode();
        inclusionRule2.put("field", "tipologiaUsoAbitazione");
        inclusionRule2.put("value", "S");
        inclusionRule2.put("operator", "eq");
        inclusionRules2.add(inclusionRule2);
        rule2.set("inclusionRules", inclusionRules2);
        q2.setRule(rule2);
        questions.add(q2);
        
        // Question 3: Visible for high ESG score companies
        Question q3 = new Question();
        q3.setId(3);
        q3.setContent("{{insuredItem.company.name}} è una compagnia sostenibile con rating ESG {{insuredItem.company.scoreESG}}. Desidera informazioni sui nostri prodotti green?");
        ObjectNode rule3 = objectMapper.createObjectNode();
        ArrayNode inclusionRules3 = objectMapper.createArrayNode();
        ObjectNode inclusionRule3 = objectMapper.createObjectNode();
        inclusionRule3.put("field", "company.scoreESG");
        inclusionRule3.put("value", "4");
        inclusionRule3.put("operator", "gte");
        inclusionRules3.add(inclusionRule3);
        rule3.set("inclusionRules", inclusionRules3);
        q3.setRule(rule3);
        questions.add(q3);
        
        return questions;
    }
}
