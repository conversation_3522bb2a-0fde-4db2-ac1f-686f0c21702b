package it.yolo.service.question;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import it.yolo.client.response.client.product.Question;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test for extended visibility service functionality including new operators:
 * count, in, contains for arrays.
 */
class ExtendedVisibilityServiceTest {

    private QuestionVisibilityService visibilityService;
    private ObjectMapper objectMapper;
    private OrderEntity orderEntity;

    @BeforeEach
    void setUp() {
        visibilityService = new QuestionVisibilityService();
        objectMapper = new ObjectMapper();
        
        setupOrderEntityWithExtendedData();
    }

    private void setupOrderEntityWithExtendedData() {
        orderEntity = new OrderEntity();
        orderEntity.setId(123L);

        OrderItemEntity orderItemEntity = new OrderItemEntity();

        // Setup insured_item with extended data
        ObjectNode insuredItem = objectMapper.createObjectNode();
        insuredItem.put("tipologiaProprietario", "P"); // Proprietario
        insuredItem.put("tipologiaFabbricato", "VM"); // Villa/Villino
        insuredItem.put("tipologiaUsoAbitazione", "S"); // Seconda casa
        insuredItem.put("value", "350000");
        
        ObjectNode company = objectMapper.createObjectNode();
        company.put("scoreESG", "4");
        company.put("name", "Test Company");
        insuredItem.set("company", company);

        orderItemEntity.setInsured_item(insuredItem);

        List<OrderItemEntity> orderItems = new ArrayList<>();
        orderItems.add(orderItemEntity);
        orderEntity.setOrderItem(orderItems);

        // Setup chosenWarranties with comprehensive data
        setupChosenWarranties();
    }

    private void setupChosenWarranties() {
        ObjectNode chosenWarranties = objectMapper.createObjectNode();
        ArrayNode warranties = objectMapper.createArrayNode();

        // Mandatory warranties
        ObjectNode fabbricato = objectMapper.createObjectNode();
        fabbricato.put("name", "Fabbricato");
        fabbricato.put("mandatory", true);
        fabbricato.put("category", "base");
        warranties.add(fabbricato);

        ObjectNode contenuto = objectMapper.createObjectNode();
        contenuto.put("name", "Contenuto");
        contenuto.put("mandatory", true);
        contenuto.put("category", "base");
        warranties.add(contenuto);

        // Optional warranties
        ObjectNode rc = objectMapper.createObjectNode();
        rc.put("name", "Responsabilità Civile");
        rc.put("mandatory", false);
        rc.put("category", "optional");
        warranties.add(rc);

        ObjectNode tutela = objectMapper.createObjectNode();
        tutela.put("name", "Tutela Legale");
        tutela.put("mandatory", false);
        tutela.put("category", "optional");
        warranties.add(tutela);

        ObjectNode furto = objectMapper.createObjectNode();
        furto.put("name", "Furto e Rapina");
        furto.put("mandatory", false);
        furto.put("category", "security");
        warranties.add(furto);

        // Create the correct structure: instance.chosenWarranties.data.warranties
        ObjectNode chosenWarrantiesData = objectMapper.createObjectNode();
        chosenWarrantiesData.set("warranties", warranties);
        chosenWarranties.set("data", chosenWarrantiesData);

        // Set in orderItem.instance.chosenWarranties
        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);
        ObjectNode instanceNode = objectMapper.createObjectNode();
        instanceNode.set("chosenWarranties", chosenWarranties);
        orderItem.setInstance(instanceNode);
    }

    // ===== COUNT OPERATOR TESTS =====

    @Test
    void testCountOperator_OptionalWarranties_GreaterThanZero() {
        Question question = new Question();
        question.setId(1);
        question.setContent("Question for optional warranties");

        // Rule: show if count of optional warranties > 0
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties[mandatory=false]");
        inclusionRule.put("operator", "count");
        inclusionRule.put("value", "0");
        inclusionRule.put("comparison", "gt");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible because there are 3 optional warranties");
    }

    @Test
    void testCountOperator_MandatoryWarranties_EqualsTwo() {
        Question question = new Question();
        question.setId(2);
        question.setContent("Question for mandatory warranties");

        // Rule: show if count of mandatory warranties = 2
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties[mandatory=true]");
        inclusionRule.put("operator", "count");
        inclusionRule.put("value", "2");
        inclusionRule.put("comparison", "eq");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible because there are exactly 2 mandatory warranties");
    }

    @Test
    void testCountOperator_SecurityWarranties_GreaterThanZero() {
        Question question = new Question();
        question.setId(3);
        question.setContent("Question for security warranties");

        // Rule: show if count of security warranties > 0
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties[category=security]");
        inclusionRule.put("operator", "count");
        inclusionRule.put("value", "0");
        inclusionRule.put("comparison", "gt");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible because there is 1 security warranty");
    }

    @Test
    void testCountOperator_NonexistentCategory_EqualsZero() {
        Question question = new Question();
        question.setId(4);
        question.setContent("Question for nonexistent category");

        // Rule: show if count of premium warranties = 0
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties[category=premium]");
        inclusionRule.put("operator", "count");
        inclusionRule.put("value", "0");
        inclusionRule.put("comparison", "eq");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible because there are 0 premium warranties");
    }

    // ===== IN OPERATOR TESTS =====

    @Test
    void testInOperator_TipologiaProprietario_ArrayFormat() {
        Question question = new Question();
        question.setId(5);
        question.setContent("Question for proprietario types");

        // Rule: show if tipologiaProprietario in ["P", "PL"]
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "tipologiaProprietario");
        inclusionRule.put("operator", "in");
        ArrayNode values = objectMapper.createArrayNode();
        values.add("P");
        values.add("PL");
        inclusionRule.set("value", values);
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible because tipologiaProprietario is 'P'");
    }

    @Test
    void testInOperator_TipologiaProprietario_StringFormat() {
        Question question = new Question();
        question.setId(6);
        question.setContent("Question for proprietario types (string format)");

        // Rule: show if tipologiaProprietario in "P,PL" (comma-separated string)
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "tipologiaProprietario");
        inclusionRule.put("operator", "in");
        inclusionRule.put("value", "P,PL");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible because tipologiaProprietario is 'P'");
    }

    @Test
    void testInOperator_TipologiaProprietario_NotInList() {
        Question question = new Question();
        question.setId(7);
        question.setContent("Question for other proprietario types");

        // Rule: show if tipologiaProprietario in ["L", "A"] (should not match "P")
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "tipologiaProprietario");
        inclusionRule.put("operator", "in");
        ArrayNode values = objectMapper.createArrayNode();
        values.add("L");
        values.add("A");
        inclusionRule.set("value", values);
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertFalse(result, "Question should be hidden because tipologiaProprietario 'P' is not in ['L', 'A']");
    }

    // ===== CONTAINS OPERATOR TESTS =====

    @Test
    void testContainsOperator_SpecificWarranty_Found() {
        Question question = new Question();
        question.setId(8);
        question.setContent("Question for Furto e Rapina warranty");

        // Rule: show if warranties contains "Furto e Rapina"
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties");
        inclusionRule.put("operator", "contains");
        inclusionRule.put("value", "Furto e Rapina");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible because 'Furto e Rapina' warranty exists");
    }

    @Test
    void testContainsOperator_SpecificWarranty_NotFound() {
        Question question = new Question();
        question.setId(9);
        question.setContent("Question for nonexistent warranty");

        // Rule: show if warranties contains "Kasko"
        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties");
        inclusionRule.put("operator", "contains");
        inclusionRule.put("value", "Kasko");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertFalse(result, "Question should be hidden because 'Kasko' warranty does not exist");
    }

    // ===== COMBINED CONDITIONS TESTS =====

    @Test
    void testCombinedConditions_WarrantyAndFabbricato() {
        Question question = new Question();
        question.setId(10);
        question.setContent("Question for specific warranty + fabbricato type");

        // Rule: show if warranties contains "Furto e Rapina" AND tipologiaFabbricato = "VM"
        ObjectNode rule = objectMapper.createObjectNode();
        rule.put("logicOperator", "AND");
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        
        ObjectNode rule1 = objectMapper.createObjectNode();
        rule1.put("field", "chosenWarranties.warranties");
        rule1.put("operator", "contains");
        rule1.put("value", "Furto e Rapina");
        inclusionRules.add(rule1);
        
        ObjectNode rule2 = objectMapper.createObjectNode();
        rule2.put("field", "tipologiaFabbricato");
        rule2.put("operator", "eq");
        rule2.put("value", "VM");
        inclusionRules.add(rule2);
        
        rule.set("inclusionRules", inclusionRules);
        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible because both conditions are satisfied");
    }

    @Test
    void testCombinedConditions_CountAndProprietario() {
        Question question = new Question();
        question.setId(11);
        question.setContent("Question for optional warranties count + proprietario type");

        // Rule: show if optional warranties count > 0 AND tipologiaProprietario in ["P", "PL"]
        ObjectNode rule = objectMapper.createObjectNode();
        rule.put("logicOperator", "AND");
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        
        ObjectNode rule1 = objectMapper.createObjectNode();
        rule1.put("field", "chosenWarranties.warranties[mandatory=false]");
        rule1.put("operator", "count");
        rule1.put("value", "0");
        rule1.put("comparison", "gt");
        inclusionRules.add(rule1);
        
        ObjectNode rule2 = objectMapper.createObjectNode();
        rule2.put("field", "tipologiaProprietario");
        rule2.put("operator", "in");
        ArrayNode values = objectMapper.createArrayNode();
        values.add("P");
        values.add("PL");
        rule2.set("value", values);
        inclusionRules.add(rule2);
        
        rule.set("inclusionRules", inclusionRules);
        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertTrue(result, "Question should be visible because both conditions are satisfied");
    }

    // ===== EDGE CASES TESTS =====

    @Test
    void testCountOperator_NullChosenWarranties() {
        orderEntity.setChosenWarranties(null);
        
        Question question = new Question();
        question.setId(12);
        question.setContent("Question with null chosenWarranties");

        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "chosenWarranties.warranties[mandatory=false]");
        inclusionRule.put("operator", "count");
        inclusionRule.put("value", "0");
        inclusionRule.put("comparison", "gt");
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertFalse(result, "Question should be hidden when chosenWarranties is null");
    }

    @Test
    void testInOperator_NullField() {
        // Change tipologiaProprietario to null
        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);
        ObjectNode insuredItem = (ObjectNode) orderItem.getInsured_item();
        insuredItem.remove("tipologiaProprietario");
        
        Question question = new Question();
        question.setId(13);
        question.setContent("Question with null field");

        ObjectNode rule = objectMapper.createObjectNode();
        ArrayNode inclusionRules = objectMapper.createArrayNode();
        ObjectNode inclusionRule = objectMapper.createObjectNode();
        inclusionRule.put("field", "tipologiaProprietario");
        inclusionRule.put("operator", "in");
        ArrayNode values = objectMapper.createArrayNode();
        values.add("P");
        values.add("PL");
        inclusionRule.set("value", values);
        inclusionRules.add(inclusionRule);
        rule.set("inclusionRules", inclusionRules);

        question.setRule(rule);

        boolean result = visibilityService.isQuestionVisible(question, orderEntity);
        assertFalse(result, "Question should be hidden when field is null");
    }
}
