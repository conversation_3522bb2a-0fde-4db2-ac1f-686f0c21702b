package it.yolo.common;

import com.ezylang.evalex.EvaluationException;
import com.ezylang.evalex.Expression;
import com.ezylang.evalex.parser.ParseException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.networknt.schema.JsonSchema;
import com.networknt.schema.JsonSchemaFactory;
import com.networknt.schema.SpecVersion;
import com.networknt.schema.ValidationMessage;
import it.yolo.client.response.client.packet.PacketResponse;
import it.yolo.entity.OrderItemEntity;
import it.yolo.exception.ValidationStructureProductException;
import it.yolo.model.CalculateCeilingsResponse;
import it.yolo.records.Packet;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.ConfigProvider;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

public class Utility {

    final static ObjectMapper MAPPER = new ObjectMapper();

    private Utility() {
        //does nothing
    }

    public static void validationProductStructure(JsonNode schemagoods, JsonNode goods) throws ValidationStructureProductException {
        JsonSchemaFactory factory = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V201909);
        JsonSchema jsonSchema = factory.getSchema(schemagoods);
        Set<ValidationMessage> errors = jsonSchema.validate(goods);
        if (!errors.isEmpty()) {
            String error = errors.stream().toList().get(0).toString();
            throw new ValidationStructureProductException("Validation Error", error);
        }
    }


    public static String generateOrderCode() {
        /*
        int orderCode = (int) (Math.random() * 100);
        String orderC = String.valueOf(orderCode);
         */
        UUID uuid = UUID.randomUUID();
        String uuidCode = String.valueOf(uuid);
        String sub = uuidCode.substring(1, 8);
//        String siglaOrderCode = "Y-";
        String siglaOrderCode = ConfigProvider.getConfig().getValue("order.prefix-code", String.class);
        return siglaOrderCode.concat(sub);
    }

    public static LocalDateTime getOrderDuration(final LocalDateTime startDate, final int duration, final String type) {
        if (StringUtils.isBlank(type)) {
            return startDate;
        }
        LocalDateTime endDate = null;
        switch (type) {
            case "day" -> {
                endDate = startDate.plusDays(duration);
            }
            case "month" -> {
                endDate = startDate.plusMonths(duration);
            }
            case "year" -> {
                endDate = startDate.plusYears(duration);
            }
        }
        if (endDate != null) {
            endDate = endDate.truncatedTo(ChronoUnit.DAYS).plusDays(1).minus(1, ChronoUnit.MILLIS);
        }
        return endDate;
    }

    public static LocalDateTime getOrderDurationV2(final LocalDateTime startDate, final int duration, final String type) {
        if (StringUtils.isBlank(type)) {
            return startDate;
        }
        LocalDateTime endDate = null;
        switch (type) {
            case "day" -> {
                endDate = startDate.plusDays(duration);
            }
            case "month" -> {
                endDate = startDate.plusMonths(duration);
            }
            case "year" -> {
                endDate = startDate.plusYears(duration);
            }
        }
        if (endDate != null) {
            endDate = endDate.minus(1, ChronoUnit.MILLIS);
        }
        return endDate;
    }

    public static LocalDateTime setStartDate(final String productCode) {
        switch (productCode) {
            case "ergo-mountain-silver":
            case "ergo-mountain-gold":
                return LocalDateTime.now();
        }
        if (productCode.startsWith("tim-for-ski")){
            return LocalDateTime.now();
        }
        return LocalDateTime.now().plusDays(1);
    }

    //war.get("externalCode")
    public static CalculateCeilingsResponse calculateCeilings(List<OrderItemEntity> orderItems, JsonNode insuredItem, PacketResponse packet) {
        ArrayNode warrantiesNode = JsonNodeFactory.instance.arrayNode();
        AtomicBoolean updated = new AtomicBoolean(false);
        Double maxInsurancePremium=packet.getData().getConfiguration().has("maxInsurancePremium") ?
                packet.getData().getConfiguration().get("maxInsurancePremium").asDouble() : null;
        Double minInsurancePremium=packet.getData().getConfiguration().has("minInsurancePremium") ?
                packet.getData().getConfiguration().get("minInsurancePremium").asDouble() : null;
        // Create a Lists to store the calculated ranges values and available values as strings
        List<String> availableValues = new ArrayList<>();

        //Set the available values based on the tipologiaCostruttivaAbitazione and tipologiaUsoAbitazione
        if(packet.getData().getProduct().getConfiguration().getProperties().has("availableValues") &&
                packet.getData().getProduct().getConfiguration().getProperties().get("availableValues").asBoolean()) {
            setAvailableValues(insuredItem, availableValues, packet.getData().getConfiguration());
        }



        packet.getData().getWarranties().forEach(warranties -> {
            if (warranties.get("anagWarranty").get("name").equals("CMS_yhomeTheftRobberyName")) {
                ArrayNode JsonavailableValues = MAPPER.valueToTree(availableValues);
                ((ObjectNode) warranties.get("ceilings")).set("available", JsonavailableValues);
            }
            if (insuredItem != null && warranties.get("ceilings").has("exp")) {
                ArrayNode expressionNode = warranties.get("ceilings").withArray("exp");
                expressionNode=calculateExpressionForPriorityFilter(warranties.get("ceilings"), orderItems);
                evaluateExp(warranties, insuredItem, maxInsurancePremium, minInsurancePremium, warrantiesNode,
                        expressionNode, updated);
            } else if(!(warranties.get("ceilings").has("exp"))){
                warrantiesNode.add(warranties);
            }

        });
        warrantiesNode.forEach(w->{
            if(w.get("ceilings").hasNonNull("priorityFilter")){
                ((ObjectNode)w.get("ceilings")).remove("priorityFilter");
            }
            if(w.get("ceilings").hasNonNull("filterCielingByOrder")){
                ((ObjectNode)w.get("ceilings")).remove("filterCielingByOrder");
            }
        });
        return new CalculateCeilingsResponse(updated.get(), warrantiesNode);
    }

    private static void evaluateExp(JsonNode warranties, JsonNode insuredItem, Double maxInsurancePremium,
                                    Double minInsurancePremium, ArrayNode warrantiesNode, ArrayNode expressionNode, AtomicBoolean updated){

        //check expression node
//        if (expressionNode.has)
        if(warranties.get("ceilings").has("range")) {
            //looping through exp node
            List<String> possibleValues = new ArrayList<>();
            expressionNode.forEach(exp -> {
                Double result = null;
                if (exp.has("min") && exp.get("min").isEmpty() && exp.get("isOp").asBoolean()) {
                    String stringExpressionMin = null;
                    if (exp.get("coefficientKey").asText().startsWith("/")) {
                        stringExpressionMin = StringUtils.remove(insuredItem.at(exp.get("coefficientKey").asText()).asText() + exp.get("operator") + exp.get("coefficientVal"), "\"");
                    } else {
                        stringExpressionMin = StringUtils.remove(insuredItem.get(exp.get("coefficientKey").asText()).asText() + exp.get("operator") + exp.get("coefficientVal"), "\"");
                    }
                    if (StringUtils.isNotBlank(StringUtils.substringAfter(stringExpressionMin, exp.get("operator").asText()))
                            && StringUtils.isNotBlank(StringUtils.substringBefore(stringExpressionMin, exp.get("operator").asText()))) {
                        //Calculate range based on exp rules
                        Expression expressionResultMin = new Expression(stringExpressionMin);
                        try {
                            if (minInsurancePremium != null) {
                                result = expressionResultMin.evaluate().getNumberValue().doubleValue() < minInsurancePremium ? minInsurancePremium : expressionResultMin.evaluate().getNumberValue().doubleValue();
                            } else {
                                result = expressionResultMin.evaluate().getNumberValue().doubleValue();
                            }
                        } catch (EvaluationException | ParseException e) {
                            throw new RuntimeException(e);
                        }
                        possibleValues.add(String.valueOf(result.intValue()));
                    }
                }
                //Make a check for a not null value! ;)
                if (exp.has("max") && exp.get("max").isEmpty() && exp.get("isOp").asBoolean()) {
                    String stringExpressionMax = null;
                    if (exp.get("coefficientKey").asText().startsWith("/")) {
                        stringExpressionMax = StringUtils.remove(insuredItem.at(exp.get("coefficientKey").asText()).asText() + exp.get("operator") + exp.get("coefficientVal"), "\"");
                    } else {
                        stringExpressionMax = StringUtils.remove(insuredItem.get(exp.get("coefficientKey").asText()).asText() + exp.get("operator") + exp.get("coefficientVal"), "\"");
                    }
                    Expression expressionResultMax = new Expression(stringExpressionMax);
                    try {
                        result = expressionResultMax.evaluate().getNumberValue().doubleValue() > maxInsurancePremium ? maxInsurancePremium : expressionResultMax.evaluate().getNumberValue().doubleValue();
                    } catch (EvaluationException | ParseException e) {
                        throw new RuntimeException(e);
                    }
                    possibleValues.add(String.valueOf(result.intValue()));
                }
                updated.set(true);
                // [min, max]
                ArrayNode ranges = MAPPER.valueToTree(possibleValues);
                // set the "preselected" with the first element of the ranges [min, max] which is "min"
                ((ObjectNode) warranties.get("ceilings")).set("preselected", ranges.get(0));
                // set the "range" with the arrayNode ranges
                ((ObjectNode) warranties.get("ceilings")).set("range", ranges);

            });
        } else if (warranties.get("ceilings").has("available")) {
            //looping through exp node
            List<String> availValues = new ArrayList<>();
            expressionNode.forEach(exp-> {
                Double result = null;
                if (exp.get("isOp").asBoolean()) {
                    String stringExpressionMin = null;
                    if(exp.get("coefficientKey").asText().startsWith("/")) {
                        stringExpressionMin = StringUtils.remove(insuredItem.at(exp.get("coefficientKey").asText()).asText() + exp.get("operator") + exp.get("coefficientVal"), "\"");
                    }else{
                        stringExpressionMin = StringUtils.remove(insuredItem.get(exp.get("coefficientKey").asText()).asText() + exp.get("operator") + exp.get("coefficientVal"), "\"");
                    }
                    if(StringUtils.isNotBlank(StringUtils.substringAfter(stringExpressionMin, exp.get("operator").asText()))
                            && StringUtils.isNotBlank(StringUtils.substringBefore(stringExpressionMin, exp.get("operator").asText()))) {
                        //Calculate range based on exp rules
                        Expression expressionResultMin = new Expression(stringExpressionMin);
                        try {
                            if(maxInsurancePremium!=null) {
                                result = expressionResultMin.evaluate().getNumberValue().doubleValue() > maxInsurancePremium ? maxInsurancePremium : expressionResultMin.evaluate().getNumberValue().doubleValue();
                            } else {
                                result = expressionResultMin.evaluate().getNumberValue().doubleValue();
                            }
                        } catch (EvaluationException | ParseException e) {
                            throw new RuntimeException(e);
                        }
                        availValues.add(String.valueOf(result.intValue()));
                    }
                } else if (exp.get("formula").isArray()) {
                    exp.withArray("formula").forEach(v->availValues.add(v.asText()));
                }
                updated.set(true);
                // [min, max]
                ArrayNode availables = MAPPER.valueToTree(availValues);
                // set the "preselected" with the first element of the ranges [min, max] which is "min"
                ((ObjectNode)warranties.get("ceilings")).set("preselected", availables.get(0));
                // set the "range" with the arrayNode ranges
                ((ObjectNode) warranties.get("ceilings")).set("available",availables);

            });
        }
        warrantiesNode.add(warranties);
    }


    private static ArrayNode calculateExpressionForPriorityFilter(JsonNode ceilings, List<OrderItemEntity> orderItems){
        if (ceilings.hasNonNull("priorityFilter") && orderItems.size()>0) { // skip for empty orderItem during update payment details
            Map<String, String> filterMap=new HashMap<>();

            ArrayNode priorityFilter=ceilings.withArray("priorityFilter");
            for (int i=0; i<priorityFilter.size(); i++){
                JsonNode filter=priorityFilter.get(i);
                extractValueFromFilter(filter, orderItems.get(0).getInsured_item(), filterMap);
            }
            return filterCeilings(filterMap, ceilings.withArray("filterCielingByOrder"), orderItems.get(0), orderItems.get(0).getInsured_item());
//        } else if(){
//            return ceilings.withArray("exp");
        } else {
            return ceilings.withArray("exp");
        }
    }



    private static void extractValueFromFilter(JsonNode filter, JsonNode insuredItem, Map<String, String> filterMap) {
        String fieldFilter = filter.get("fieldFilter").asText();
        if(fieldFilter.startsWith("/")){
            if (fieldFilter.contains("arr_index")) {
                List<String> values = new ArrayList<>();
                //array stream
                String split = fieldFilter.split("/arr_index")[0];
                if(insuredItem.at(split).isEmpty()){
                    return;
                }
                ArrayNode array = (ArrayNode) insuredItem.at(split);
                for (int i = 0; i < array.size(); i++) {
                    String currentPath = fieldFilter.replace("arr_index", Integer.toString(i));
                    values.add(insuredItem.at(currentPath).asText());
                }
                ArrayNode priorities = filter.withArray("values");
                for (int i = 0; i < priorities.size(); i++) {
                    if (values.contains(priorities.get(i).asText())) {
                        filterMap.put(fieldFilter, priorities.get(i).asText());
                        break;
                    }
                }
            } else {
                filterMap.put(fieldFilter, insuredItem.at(fieldFilter).asText());
            }
        }
    }

    private static ArrayNode filterCeilings(Map<String, String> filtermap, ArrayNode ceilingsToFilter, OrderItemEntity orderItem, JsonNode insuredItem){
        //LIMITAZIONE: GESTITE SOLO 2 FILTER CONDITION IN AND
        ArrayNode possibleCeilings=JsonNodeFactory.instance.arrayNode();
        for(int i=0; i<ceilingsToFilter.size(); i++){
            JsonNode ceilToFilter=ceilingsToFilter.get(i);
            ArrayNode conditions=ceilToFilter.withArray("filterOrderCondition");
            Boolean accepted=Boolean.FALSE;
            for(int j=0; j<conditions.size(); j++){
                JsonNode condition=conditions.get(j);
                accepted=evaluateCondition(condition, filtermap, orderItem, insuredItem);
                if(!accepted){
                    //next available ceilings
                    break;
                }
            }
            if(accepted){
                possibleCeilings=ceilToFilter.withArray("availableCeilings");
                break;
            }
        }
        return possibleCeilings;
    }

    private static Boolean evaluateCondition(JsonNode condition, Map<String, String> filterMap, OrderItemEntity orderItem, JsonNode insuredItem){
        String fieldFilter=condition.get("fieldFilter").asText();
        Boolean accepted=Boolean.FALSE;
        if(fieldFilter.startsWith("/")) {
            List<String> conditionFieldValues=new ArrayList<>();
            condition.withArray("fieldValue").forEach(v->conditionFieldValues.add(v.asText()));
            if(filterMap.containsKey(fieldFilter) && conditionFieldValues.contains(filterMap.get(fieldFilter))){
                accepted=true;
            }
        } else if (fieldFilter.equalsIgnoreCase("RULE_DURATION")) {
            String stringExp=Long.toString(ChronoUnit.DAYS.between(orderItem.getStart_date(), orderItem.getExpiration_date().plusDays(1)))
                    .concat(condition.get("fieldOperator").asText()).concat(condition.get("fieldValue").asText());
            Expression expression = new Expression(stringExp);
            try {
                accepted=expression.evaluate().getBooleanValue();
            } catch (EvaluationException | ParseException e) {
                throw new RuntimeException(e);
            }
        }
        return accepted;
    }

    public static void setAvailableValues(JsonNode insuredItem, List<String> availableValues, JsonNode packetConfiguration) {
        String tipologiaUsoAbitazione = insuredItem.get("tipologiaUsoAbitazione").asText();
        String tipologiaCostruttivaAbitazione = insuredItem.get("tipologiaCostruttivaAbitazione").asText();
        if(tipologiaUsoAbitazione!=null && tipologiaCostruttivaAbitazione !=null && packetConfiguration.has(tipologiaUsoAbitazione)){
            if(packetConfiguration.get(tipologiaUsoAbitazione).isArray()){
                packetConfiguration.withArray(tipologiaUsoAbitazione).forEach(v->{
                    availableValues.add(v.textValue());
                });
            } else if (packetConfiguration.get(tipologiaUsoAbitazione).has(tipologiaCostruttivaAbitazione)) {
                packetConfiguration.get(tipologiaUsoAbitazione).withArray(tipologiaCostruttivaAbitazione).forEach(v->{
                    availableValues.add(v.textValue());
                });
            }
        }
    }


    public static PacketResponse setPacketWithCieling(Packet packet, OrderItemEntity orderItem) {
        if (orderItem.getInstance() != null && orderItem.getInstance().get("ceilings") != null) {
            packet.packetResponse().getData().setWarranties(orderItem.getInstance().get("ceilings"));
        }
        return packet.packetResponse();
    }



    public static String convertToDateFormat(String inputDate) {
        String[] formats = {
                "dd/MM/yyyy",
                "yyyy-MM-dd",
                "MM/dd/yyyy",
                "yyyy/MM/dd",
                "dd-MM-yyyy",
                "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
        };
        LocalDate date = null;
        for (String format : formats) {
            try {
                if (format.equals("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")) {
                    OffsetDateTime offsetDateTime = OffsetDateTime.parse(inputDate);
                    date = offsetDateTime.toLocalDate();
                } else {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
                    date = LocalDate.parse(inputDate, formatter);
                }
                break;
            } catch (Exception ignored) {
            }
        }

        if (date != null) {
            return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } else {
            return null;
        }
    }
}
