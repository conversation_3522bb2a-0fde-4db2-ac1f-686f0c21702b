package it.yolo.service.question;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service for processing question templates with dynamic placeholder replacement.
 * 
 * This service replaces placeholders in question content with actual values from OrderEntity.
 * Placeholder format: {{campo.sottocampo}} (e.g., {{orderItem.insuredAge}}, {{insuredItem.value}})
 * 
 * Supported data sources:
 * - orderItem: fields from OrderItemEntity
 * - insuredItem: fields from the insured_item JsonNode
 * - order: fields from OrderEntity
 */
@ApplicationScoped
@Slf4j
public class QuestionTemplatingService {

    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{\\{([^}]+)\\}\\}");

    /**
     * Process question content by replacing placeholders with actual values from order data.
     *
     * @param content The question content containing placeholders
     * @param orderEntity The order entity containing the data
     * @return The processed content with placeholders replaced
     */
    public String processTemplate(String content, OrderEntity orderEntity) {
        if (content == null || content.trim().isEmpty()) {
            return content;
        }

        if (orderEntity == null) {
            log.warn("OrderEntity is null, returning original content");
            return content;
        }

        log.debug("Processing template: {}", content);

        String processedContent = content;
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(content);

        while (matcher.find()) {
            String placeholder = matcher.group(0); // Full placeholder including {{}}
            String fieldPath = matcher.group(1);   // Field path without {{}}
            
            log.debug("Processing placeholder: {} with field path: {}", placeholder, fieldPath);
            
            String value = extractValue(fieldPath, orderEntity);
            if (value != null) {
                processedContent = processedContent.replace(placeholder, value);
                log.debug("Replaced placeholder {} with value: {}", placeholder, value);
            } else {
                log.debug("No value found for placeholder: {}, keeping original", placeholder);
            }
        }

        log.debug("Template processing completed. Original: {}, Processed: {}", content, processedContent);
        return processedContent;
    }

    /**
     * Extract value from order entity based on field path.
     *
     * @param fieldPath The field path (e.g., "orderItem.price", "insuredItem.company.scoreESG")
     * @param orderEntity The order entity
     * @return The extracted value as string, or null if not found
     */
    private String extractValue(String fieldPath, OrderEntity orderEntity) {
        if (fieldPath == null || fieldPath.trim().isEmpty()) {
            return null;
        }

        String[] pathParts = fieldPath.split("\\.", 2);
        String rootField = pathParts[0];
        String remainingPath = pathParts.length > 1 ? pathParts[1] : null;

        log.debug("Extracting value for root field: {} with remaining path: {}", rootField, remainingPath);

        switch (rootField) {
            case "orderItem":
                return extractFromOrderItem(remainingPath, orderEntity);
            case "insuredItem":
                return extractFromInsuredItem(remainingPath, orderEntity);
            case "order":
                return extractFromOrder(remainingPath, orderEntity);
            default:
                log.debug("Unknown root field: {}", rootField);
                return null;
        }
    }

    /**
     * Extract value from OrderItemEntity.
     *
     * @param fieldPath The field path within order item
     * @param orderEntity The order entity
     * @return The extracted value as string, or null if not found
     */
    private String extractFromOrderItem(String fieldPath, OrderEntity orderEntity) {
        if (orderEntity.getOrderItem() == null || orderEntity.getOrderItem().isEmpty()) {
            log.debug("No order items available");
            return null;
        }

        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);
        
        if (fieldPath == null) {
            return null;
        }

        log.debug("Extracting from order item field: {}", fieldPath);

        // Handle direct fields of OrderItemEntity
        switch (fieldPath) {
            case "price":
                return orderItem.getPrice();
            case "annualPrice":
                return orderItem.getAnnualPrice();
            case "productId":
                return String.valueOf(orderItem.getProduct_id());
            case "policyNumber":
                return orderItem.getPolicy_number();
            case "masterPolicyNumber":
                return orderItem.getMaster_policy_number();
            case "externalId":
                return orderItem.getExternal_id();
            case "state":
                return orderItem.getState();
            case "quantity":
                return orderItem.getQuantity() != null ? orderItem.getQuantity().toString() : null;
            case "packetId":
                return orderItem.getPacketId() != null ? orderItem.getPacketId().toString() : null;
            default:
                log.debug("Unknown order item field: {}", fieldPath);
                return null;
        }
    }

    /**
     * Extract value from insured_item JsonNode.
     *
     * @param fieldPath The field path within insured item
     * @param orderEntity The order entity
     * @return The extracted value as string, or null if not found
     */
    private String extractFromInsuredItem(String fieldPath, OrderEntity orderEntity) {
        if (orderEntity.getOrderItem() == null || orderEntity.getOrderItem().isEmpty()) {
            log.debug("No order items available");
            return null;
        }

        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);
        JsonNode insuredItem = orderItem.getInsured_item();
        
        if (insuredItem == null || fieldPath == null) {
            log.debug("Insured item is null or field path is null");
            return null;
        }

        log.debug("Extracting from insured item field: {}", fieldPath);

        // Convert field path to JsonPointer format
        String jsonPointer = "/" + fieldPath.replace(".", "/");
        JsonNode valueNode = insuredItem.at(jsonPointer);

        if (!valueNode.isMissingNode()) {
            String value = valueNode.asText();
            log.debug("Found value {} for insured item field {}", value, fieldPath);
            return value;
        } else {
            log.debug("Field {} not found in insured item", fieldPath);
            return null;
        }
    }

    /**
     * Extract value from OrderEntity.
     *
     * @param fieldPath The field path within order
     * @param orderEntity The order entity
     * @return The extracted value as string, or null if not found
     */
    private String extractFromOrder(String fieldPath, OrderEntity orderEntity) {
        if (fieldPath == null) {
            return null;
        }

        log.debug("Extracting from order field: {}", fieldPath);

        // Handle direct fields of OrderEntity
        switch (fieldPath) {
            case "id":
                return orderEntity.getId() != null ? orderEntity.getId().toString() : null;
            case "orderCode":
                return orderEntity.getOrderCode();
            case "policyCode":
                return orderEntity.getPolicyCode();
            case "packetId":
                return orderEntity.getPacketId() != null ? orderEntity.getPacketId().toString() : null;
            case "productId":
                return orderEntity.getProductId() != null ? orderEntity.getProductId().toString() : null;
            case "brokerId":
                return String.valueOf(orderEntity.getBrokerId());
            case "companyId":
                return String.valueOf(orderEntity.getCompanyId());
            case "customerId":
                return orderEntity.getCustomerId() != null ? orderEntity.getCustomerId().toString() : null;
            case "insurancePremium":
                return orderEntity.getInsurancePremium() != null ? orderEntity.getInsurancePremium().toString() : null;
            case "createdBy":
                return orderEntity.getCreatedBy();
            case "updatedBy":
                return orderEntity.getUpdatedBy();
            case "productType":
                return orderEntity.getProductType();
            default:
                log.debug("Unknown order field: {}", fieldPath);
                return null;
        }
    }
}
