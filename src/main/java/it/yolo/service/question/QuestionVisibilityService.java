package it.yolo.service.question;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import it.yolo.client.response.client.product.Question;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for evaluating question visibility rules.
 * 
 * This service determines whether questions should be visible based on JSON rules
 * stored in the question's rule field. It reuses the rule evaluation logic from
 * PacketRuleEngine to maintain consistency.
 * 
 * Rule format example:
 * {
 *   "exclusionRules": [
 *     {
 *       "field": "company.scoreESG",
 *       "value": "3",
 *       "operator": "gt"
 *     }
 *   ]
 * }
 * 
 * Supported operators: eq, ne, gt, lt, gte, lte, contains
 */
@ApplicationScoped
@Slf4j
public class QuestionVisibilityService {

    /**
     * Determine if a question should be visible based on its visibility rules.
     *
     * @param question The question to evaluate
     * @param orderEntity The order entity containing the data for rule evaluation
     * @return true if the question should be visible, false otherwise
     */
    public boolean isQuestionVisible(Question question, OrderEntity orderEntity) {
        if (question == null) {
            log.debug("Question is null, not visible");
            return false;
        }

        if (orderEntity == null) {
            log.warn("OrderEntity is null, showing question by default");
            return true;
        }

        JsonNode rule = question.getRule();
        if (rule == null || rule.isNull()) {
            log.debug("Question {} has no visibility rules, showing by default", question.getId());
            return true;
        }

        log.debug("Evaluating visibility rules for question {}", question.getId());

        // Get the first order item for rule evaluation
        if (orderEntity.getOrderItem() == null || orderEntity.getOrderItem().isEmpty()) {
            log.debug("No order items available for rule evaluation, showing question by default");
            return true;
        }

        OrderItemEntity orderItem = orderEntity.getOrderItem().get(0);

        // Check exclusion rules first - if any exclusion rule is met, hide the question
        if (rule.has("exclusionRules")) {
            log.debug("Checking exclusion rules for question {}", question.getId());
            ArrayNode exclusionRules = (ArrayNode) rule.get("exclusionRules");
            for (JsonNode exclusionRule : exclusionRules) {
                if (evaluateCondition(exclusionRule, orderItem)) {
                    log.debug("Question {} hidden by exclusion rule: {}", question.getId(), exclusionRule);
                    return false;
                }
            }
            log.debug("Question {} passed all exclusion rules", question.getId());
        }

        // If there are no inclusion rules, show the question (it passed exclusion rules)
        if (!rule.has("inclusionRules") || rule.get("inclusionRules").isEmpty()) {
            log.debug("Question {} has no inclusion rules, showing after passing exclusion checks", question.getId());
            return true;
        }

        // Check inclusion rules
        log.debug("Checking inclusion rules for question {}", question.getId());
        ArrayNode inclusionRules = (ArrayNode) rule.get("inclusionRules");

        // Determine logic operator (default to AND)
        String logicOperator = rule.has("logicOperator") ?
                              rule.get("logicOperator").asText().toUpperCase() : "AND";

        log.debug("Using logic operator {} for inclusion rules of question {}", logicOperator, question.getId());

        if ("OR".equals(logicOperator)) {
            // OR logic - at least one rule must be satisfied
            for (JsonNode inclusionRule : inclusionRules) {
                if (evaluateCondition(inclusionRule, orderItem)) {
                    log.debug("Question {} shown by OR inclusion rule: {}", question.getId(), inclusionRule);
                    return true;
                }
            }
            log.debug("Question {} hidden because no OR inclusion rules were satisfied", question.getId());
            return false;
        } else {
            // AND logic - all rules must be satisfied
            for (JsonNode inclusionRule : inclusionRules) {
                if (!evaluateCondition(inclusionRule, orderItem)) {
                    log.debug("Question {} hidden because AND inclusion rule not satisfied: {}", question.getId(), inclusionRule);
                    return false;
                }
            }
            log.debug("Question {} shown because all AND inclusion rules were satisfied", question.getId());
            return true;
        }
    }

    /**
     * Evaluate a single rule condition.
     * This method reuses the logic from PacketRuleEngine.evaluateCondition.
     *
     * @param condition The rule condition to evaluate
     * @param orderItem The order item containing the data
     * @return true if the condition is satisfied, false otherwise
     */
    private boolean evaluateCondition(JsonNode condition, OrderItemEntity orderItem) {
        if (!condition.has("field") || !condition.has("value")) {
            log.debug("Condition missing required fields, assuming it matches");
            return true;
        }

        String field = condition.get("field").asText();
        String expectedValue = condition.get("value").asText();
        String operator = condition.has("operator") ? condition.get("operator").asText() : "eq";
        boolean negate = condition.has("negate") && condition.get("negate").asBoolean();

        log.debug("Evaluating condition: field={}, value={}, operator={}, negate={}", field, expectedValue, operator, negate);

        // Get actual value from order data
        String actualValue = getFieldValue(field, orderItem);

        // If actual value is null, condition is not satisfied
        if (actualValue == null) {
            log.debug("Field {} not found in order data", field);
            return negate; // If negated, return true when field is not found
        }

        log.debug("Comparing values: actual={}, expected={}", actualValue, expectedValue);

        boolean result;
        // Evaluate condition based on operator
        switch (operator) {
            case "eq":
                result = actualValue.equals(expectedValue);
                break;
            case "ne":
                result = !actualValue.equals(expectedValue);
                break;
            case "gt":
                try {
                    result = Double.parseDouble(actualValue) > Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for gt comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "lt":
                try {
                    result = Double.parseDouble(actualValue) < Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for lt comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "gte":
                try {
                    result = Double.parseDouble(actualValue) >= Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for gte comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "lte":
                try {
                    result = Double.parseDouble(actualValue) <= Double.parseDouble(expectedValue);
                } catch (NumberFormatException e) {
                    log.debug("Error parsing numeric values for lte comparison: {}", e.getMessage());
                    result = false;
                }
                break;
            case "contains":
                result = actualValue.contains(expectedValue);
                break;
            default:
                log.debug("Unknown operator: {}", operator);
                result = false;
        }

        // Apply negation if specified
        if (negate) {
            result = !result;
        }

        log.debug("Condition evaluation result: {}", result);
        return result;
    }

    /**
     * Get field value from order data using the same logic as PacketRuleEngine.
     *
     * @param field The field path (e.g., "company.scoreESG", "tipologiaUsoAbitazione")
     * @param orderItem The order item containing the data
     * @return The field value as string, or null if not found
     */
    private String getFieldValue(String field, OrderItemEntity orderItem) {
        JsonNode insuredItem = orderItem.getInsured_item();
        if (insuredItem == null) {
            log.debug("No insured item data available");
            return null;
        }

        // Convert field to JsonPointer format if needed
        String jsonPointer = field;
        if (!jsonPointer.startsWith("/")) {
            // Convert dot notation to JsonPointer format
            jsonPointer = "/" + field.replace(".", "/");
        }

        log.debug("Looking for field {} using JsonPointer {}", field, jsonPointer);

        // Use at() to get the value at the specified path
        JsonNode valueNode = insuredItem.at(jsonPointer);

        // Check if the node exists and is not missing
        if (!valueNode.isMissingNode()) {
            String value = valueNode.asText();
            log.debug("Found value {} for field {}", value, field);
            return value;
        } else {
            log.debug("Field {} not found in insured item", field);
            return null;
        }
    }
}
