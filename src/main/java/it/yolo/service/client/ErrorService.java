package it.yolo.service.client;

import com.fasterxml.jackson.databind.JsonNode;
import it.yolo.client.IadConfigurationClient;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

@ApplicationScoped
public class ErrorService {

    @Inject
    @RestClient
    IadConfigurationClient iadConfigurationClient;

    @Inject
    Logger log;

    public static final String INSURED_TOTAL_MISMATCH = "insured_age_not_matched";
    private static final String DEFAULT_ERROR_MESSAGE_TOTAL_MISMATCH = "Errore generico: assicurati che i valori siano corretti.";
    private static final String DEFAULT_ERROR_MESSAGE_COMPANY_DATA = "I dati dell'azienda non corrispondono a quelli del cliente.";

    public String insuredsTotalMismatchError(String token) {
        return getErrorMessage(token, INSURED_TOTAL_MISMATCH, DEFAULT_ERROR_MESSAGE_TOTAL_MISMATCH);
    }
    
    public String companyDataValidationError(String token, String errorCode) {
        return getErrorMessage(token, errorCode, DEFAULT_ERROR_MESSAGE_COMPANY_DATA);
    }
    
    /**
     * Retrieves all error messages from the configuration service
     * and filters them based on the provided errorCode.
     *
     * @param token the authentication token
     * @param errorCode the error code
     * @param defaultErrorMessage the default error message
     * @return the error message
     */
    private String getErrorMessage(String token, String errorCode, String defaultErrorMessage) {
        if (token == null || token.isEmpty()) {
            log.error("Token is null or empty");
            return defaultErrorMessage;
        }

        log.infov("Fetching errors for token: {0}", token);
        JsonNode errors = iadConfigurationClient.getErrors(token);
        log.infov("Errors list: {0}", errors.toString());

        for (JsonNode error : errors) {
            if (error.get("code").asText().equalsIgnoreCase(errorCode)) {
                String errorMessage = error.get("value").asText();
                log.infov("Matched error message: {0}", errorMessage);
                return errorMessage;
            }
        }

        log.warn("No matching error code found for: " + errorCode);
        return defaultErrorMessage;
    }
}

