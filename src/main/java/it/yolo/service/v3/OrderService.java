package it.yolo.service.v3;

import be.digitech.iadtoken.grpc.Empty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.opentelemetry.instrumentation.annotations.SpanAttribute;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.PgwClient;
import it.yolo.client.PolicyClientV2;
import it.yolo.client.SurveyClient;
import it.yolo.client.WarrantyRulesClient;
import it.yolo.client.communicationManager.dto.*;
import it.yolo.client.communicationManager.template.dto.TemplateRequest;
import it.yolo.client.communicationManager.template.netInsurance.NetHomeflix;
import it.yolo.client.communicationManager.utils.AttachmentUtils;
import it.yolo.client.document.dto.CertificateRequestDto;
import it.yolo.client.document.dto.PolicyResponseDto;
import it.yolo.client.document.dto.UploadCertificateRequestDto;
import it.yolo.client.grpc.IadTokenGrpcClient;
import it.yolo.client.request.PgwRequest;
import it.yolo.client.response.PgwEstimateResponse;
import it.yolo.client.response.client.packet.PacketResponse;
import it.yolo.client.response.client.packet.Product;
import it.yolo.client.response.client.product.ProductResponse;
import it.yolo.client.response.client.product.UtmSourcesDto;
import it.yolo.common.CommonUtils;
import it.yolo.common.JwtGroupsUtils;
import it.yolo.common.Utility;
import it.yolo.constants.OrderStateEnum;
import it.yolo.entity.AnagStatesEntity;
import it.yolo.entity.OrderEntity;
import it.yolo.entity.OrderItemEntity;
import it.yolo.exception.*;
import it.yolo.mapper.custom.MapperOrder;
import it.yolo.model.*;
import it.yolo.records.Catalog;
import it.yolo.records.Packet;
import it.yolo.repository.AnagStatesRepository;
import it.yolo.repository.OrderItemRepository;
import it.yolo.repository.OrderRepository;
import it.yolo.service.OrderHistoryService;
import it.yolo.service.PgwService;
import it.yolo.service.client.*;
import it.yolo.service.rule.PacketRuleEngine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.json.JsonString;
import javax.transaction.Transactional;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/*
 * ORDER-SERVICE V3 --> GUARDARE SEMPRE L'IMPORT
 */


@Slf4j
@RequestScoped
public class OrderService {

    @Inject
    OrderRepository repo;


    @Inject
    AnagStatesRepository stateRepo;

    @Inject
    OrderItemRepository itemRepo;

    @Inject
    OrderHistoryService orderHistoryService;

    @Inject
    PgwService pgwService;

    @Inject
    ServiceClientCatalog serviceClientCatalog;

    @Inject
    ServiceClientProductV3 serviceClientProduct;

    @Inject
    ServiceClientPacketsV3 serviceClientPacket;

    @Inject
    @RestClient
    WarrantyRulesClient warrantyRulesClient;

    @Inject
    @RestClient
    PolicyClientV2 policyClient;

    @Inject
    @RestClient
    PgwClient pgwClient;

    @Inject
    @RestClient
    SurveyClient surveyClient;

    @Inject
    ServiceClientCustomer serviceClientCustomer;

    @Inject
    ServiceClientComunication serviceClientComunication;

    @Inject
    ServiceClientDocument serviceClientDocument;

    @Inject
    JsonWebToken jsonWebToken;

    @Inject
    NetHomeflix netHomeflix;

    @Inject
    OrderItemCheckService orderItemCheckService;

    @Inject
    PacketRuleEngine packetRuleEngine;

    @ConfigProperty(name="tch.usr")
    String tchUser;

    @ConfigProperty(name = "intermediary.users")
    String intermediaryUser;

    @ConfigProperty(name = "technical.users")
    String technicalUser;

    @Inject
    CommonUtils commonUtils;

    @Inject
    Logger logger;

    @Inject
    JwtUtilsService jwtUtilsService;

    @Inject
    IadTokenGrpcClient tokenGrpcClient;

    @ConfigProperty(name = "tch.user.intermediari.username", defaultValue = "null")
    String techIntermediary;

    private static String STATE_ORDER_CREATE = "Draft";
    private static String STATE_ORDER_ESTIMATE = "Estimate";
    public static String INSURANCE = "insurance_info";
    //variabile uguale a 0 in quanto a oggi la corrispondenza tra order e product è univoca
    private static int ORDER_ITEM = 0;

    private static String YOLO = "yolo";

    @Transactional
    @WithSpan("OrderService.createOrder")
    public OrderEntity createOrder(String token,
                                   @SpanAttribute("arg.entity") OrderEntity entity,
                                   String sessionId) throws Exception {
        logger.info("TOKEN AUTH ONED FUORI ANONIMO: " + jsonWebToken.getRawToken()); //REMOVE
        assignSessionIdToOrder(entity, sessionId);
        assignCustomerToOrder(entity);
        Long packetId=Long.valueOf(entity.getPacketId());
        Packet packetResponse= invokePacketByPacketId(Long.valueOf(packetId), token);
        entity.setProductId(packetResponse.packetResponse().getData().getProduct().getId());
        entity.setProductType(packetResponse.packetResponse().getData().getProduct().getProductType());
        List<OrderItemEntity> list=MapperOrder.OrderToOrderItemListV3(packetResponse, INSURANCE, entity);
        entity.setOrderItem(list);
        entity.setOrderCode(Utility.generateOrderCode());
        AnagStatesEntity stateOrderCreate = stateRepo.find("state", STATE_ORDER_CREATE).firstResult();
        entity.setId(null);
        entity.setIntermediaryOrder(checkTokenIntermediary());
        entity.setAnagStates(stateOrderCreate);
        if(StringUtils.isNotBlank(entity.getUtmSource())){
            boolean utmSourceFound=false;
            ProductResponse productResponse=invokeProductByProductId(Long.valueOf(entity.getProductId()), token);
            for(UtmSourcesDto utmSource: productResponse.getData().getUtmSources()){
                if(utmSource.getCode().equalsIgnoreCase(entity.getUtmSource())){
                    utmSourceFound=true;
                    break;
                }
            }
            if(!utmSourceFound){
                entity.setUtmSource(null);
            } else {
                entity.setUtmSource(entity.getUtmSource());
            }
        }
        repo.persist(entity);
        orderHistoryService.createHistoryForOrderInsert(entity);
        entity.setStepState(commonUtils.getStepState(entity.getProductId()));
        return entity;
    }

    private void assignCustomerToOrder(OrderEntity entity) throws Exception {
        Set<String> groups = jwtUtilsService.extractGroupsCognito();
        if(groups.contains("yolo-users") || groups.contains("sso-users")) {
            String ndg = jsonWebToken.getClaim("username");
            Integer idCustomer = serviceClientCustomer.findByNdg(ndg).get("data").get("id").asInt();
            entity.setCustomerId(idCustomer);
        } else if(groups.contains("technical-users") || groups.contains("anonimo") || groups.contains("intermediary-anonimo")) {
            Empty empty = null;
            String token = tokenGrpcClient.technicalLogin(empty).getToken();
            String ndg = jwtUtilsService.extractUsernameFromAnotherToken(token);
            Integer idCustomer = serviceClientCustomer.findByNdg(ndg).get("data").get("id").asInt();
            entity.setCustomerId(idCustomer);
        } else if(groups.contains("intermediary-users")) {
            Integer idCustomer = serviceClientCustomer.findByNdg(techIntermediary).get("data").get("id").asInt();
            entity.setCustomerId(idCustomer);
        } else {
            throw new RuntimeException("");
        }
    }


    @WithSpan("OrderService.readOrder")
    public OrderEntity readOrder(
            @SpanAttribute("arg.id") Long id, String sessionId) {
        OrderEntity entity=repo.findById(id);
        checkSessionId(entity, sessionId);
        if(entity!=null){
            entity.setStepState(commonUtils.getStepState(entity.getProductId()));
        }else {
            throw new EntityNotFoundException("Entity not found", String.format(
                    "Entity order by id=%d not found", id));
        }
        entity.setSurvey(getAnswers(entity.getOrderCode(), "Bearer "+jsonWebToken.getRawToken()));
        return entity;
    }

    @WithSpan("OrderService.readOrderByOrderCode")
    public OrderEntity readOrderByOrderCode(
            @SpanAttribute("arg.id") String orderCode, String sessionId) {
        OrderEntity entity=repo.find("order_code", orderCode).firstResult();
        checkSessionId(entity, sessionId);
        if(entity!=null){
            entity.setStepState(commonUtils.getStepState(entity.getProductId()));
        }else {
            throw new EntityNotFoundException("Entity not found", String.format(
                    "Entity order by code not found: "+orderCode));
        }
        entity.setSurvey(getAnswers(entity.getOrderCode(), "Bearer "+jsonWebToken.getRawToken()));
        return entity;
    }

    public OrderEntity readOrderByOrderCodeEstimateKind(
            @SpanAttribute("arg.id") String orderCode) {
        OrderEntity entity=repo.find("order_code", orderCode).firstResult();
        if(entity!=null){
            entity.setStepState(commonUtils.getStepState(entity.getProductId()));
        }else {
            throw new EntityNotFoundException("Entity not found", String.format(
                    "Entity order by code not found: "+orderCode));
        }
        return entity;
    }

    @WithSpan("OrderService.readOrder")
    public OrderEntity uncheckedReadOrder(
            @SpanAttribute("arg.id") Long id) {
        OrderEntity order= repo.findById(id);
        if(order!=null){
            order.setStepState(commonUtils.getStepState(order.getProductId()));
        }else {
            throw new EntityNotFoundException("Entity not found", String.format(
                    "Entity order by id=%d not found", id));
        }
        order.setSurvey(getAnswers(order.getOrderCode(), "Bearer "+jsonWebToken.getRawToken()));
        return order;
    }

    @WithSpan("OrderService.readOrderByOrderCode")
    public OrderEntity readOrderByOrderCodeUnchecked(
            @SpanAttribute("arg.id") String orderCode) throws Exception {
        OrderEntity order = repo.find("order_code", orderCode).firstResult();
        order.setSurvey(getAnswers(order.getOrderCode(), "Bearer "+jsonWebToken.getRawToken()));
        return order;
    }

    @Transactional
    @WithSpan("OrderService.updateOrder")
    public OrderEntity updateOrder(
            @SpanAttribute("arg.id") String order_code,
            @SpanAttribute("arg.xTenantLanguage") String xTenantLanguage,
            @SpanAttribute("arg.entity") OrderEntity entity,
            @SpanAttribute("arg.unchecked") Boolean unchecked,
            @SpanAttribute("arg.customerId") Integer customerId,
            String token, String sessionId) throws Exception {


        OrderEntity resultDb = repo.find("order_code", order_code).firstResult();
        checkSessionId(resultDb, sessionId);
//        checkOrderAndCustomer(resultDb);
        if(resultDb == null){
            throw new EntityNotFoundException(" Entity order by order_code=%d not found, id ", order_code);
        }
        String ndg=jsonWebToken.getClaim("username");
        if(!resultDb.getIntermediaryOrder()){
            resultDb.setIntermediaryOrder(checkTokenIntermediary());
        }
        if(jwtUtilsService.extractGroupsCognito().contains("anonimo")){
            assignCustomerToOrder(entity);
        } else {
            if(!unchecked) {
                Integer idCustomer=serviceClientCustomer.findByNdg(ndg).get("data").get("id").asInt();
                //chiamata non proveniente da emission manager (per pagamenti ricorrenti)
                if(ndg.equalsIgnoreCase(tchUser) && !Objects.equals(resultDb.getCustomerId(), idCustomer)){
                    //era customer -> è tch
                    throw new WebApplicationException(Response.status(403).entity("Forbidden").build());
                } else if (!ndg.equalsIgnoreCase(tchUser)){
                    //è customer
                    if(idCustomer.intValue()!=resultDb.getCustomerId().intValue()) {
                        Integer idCustomerTch = serviceClientCustomer.findByNdg(tchUser).get("data").get("id").asInt();
                        if(idCustomerTch==resultDb.getCustomerId()) {
                            //era tch -> ok
                            resultDb.setCustomerId(idCustomer);
                        } else {
                            //non era tch e id customer token diverso da id su order
                            throw new WebApplicationException(Response.status(403).entity("Forbidden").build());
                        }
                    }
                }
            } else if(unchecked && customerId!=null){
                resultDb.setCustomerId(customerId);
            }
        }
        if(!StringUtils.isEmpty(xTenantLanguage)) {
            resultDb.setLanguage(xTenantLanguage);
            entity.setLanguage(xTenantLanguage);
        }

        String agenziaDiRiferimento = entity.getAgenziaDiRiferimento();
        if (agenziaDiRiferimento != null) {
            resultDb.setAgenziaDiRiferimento(agenziaDiRiferimento);
        }
        if(entity.getProductId() == null) {
            entity.setProductId(resultDb.getProductId());
        }
        if(entity.getPacketId()!=null){
            resultDb.setPacketId(entity.getPacketId());
        }
        Packet packetResponse = checkPacketFromOrderItem(entity, resultDb, token);

        // Call the new method to get filtered packets and update instance.filterPackets
        checkPacketsFromOrderItem(entity, resultDb, token);

        entity.setProductId(packetResponse.packetResponse().getData().getProduct().getId());
        entity.setPacketId(packetResponse.packetResponse().getData().getId());

        if(entity.getFieldToRecover()!=null){
            resultDb.setFieldToRecover(entity.getFieldToRecover());
        }
        if(entity.getPaymentToken()!=null && !entity.getPaymentToken().isEmpty()){
            resultDb.setPaymentToken(entity.getPaymentToken());
        }
        if(entity.getPaymentTransactionId()!=null) {
            resultDb.setPaymentTransactionId(entity.getPaymentTransactionId());
        }
        if(entity.getPaymentType() !=null && !entity.getPaymentType().isEmpty()){
            resultDb.setPaymentType(entity.getPaymentType());
        }
        if(!Objects.equals(entity.getProductId(), resultDb.getProductId())){
            resultDb.setProductId(entity.getProductId());
        }
        String group = JwtGroupsUtils.getGroup(jsonWebToken,technicalUser,intermediaryUser);
        if(!group.equalsIgnoreCase(technicalUser) && !group.equalsIgnoreCase(intermediaryUser)) {
            Set<String> groups = jwtUtilsService.extractGroupsCognito();
            if(groups.contains("yolo-users") || groups.contains("sso-users")) {
                String ndgCustomer = jsonWebToken.getClaim("username");
                String birthDateCustomer = serviceClientCustomer.findByNdg(ndgCustomer).get("data").get("date_of_birth").asText();
                if (!entity.getOrderItem().isEmpty() && entity.getOrderItem().get(0).getInsured_item().get("insurance_holders") != null){
                    if(entity.getOrderItem().get(0).getInsured_item().get("insurance_holders") == null || entity.getOrderItem().get(0).getInsured_item().get("insurance_holders").isEmpty()){
                        orderItemCheckService.checkAge(packetResponse.packetResponse().getData().getProduct(), ndg, LocalDateTime.now(), birthDateCustomer);
                    } else {
                        JsonNode insuranceHolderBirthDate = entity.getOrderItem().get(0).getInsured_item()
                                                                .get("insurance_holders").get(0).get("birth_date");
                        if(!insuranceHolderBirthDate.isNull() && !insuranceHolderBirthDate.asText().isEmpty()) {
                            String birthDate = entity.getOrderItem().get(0).getInsured_item().get("insurance_holders")
                                                     .get(0).get("birth_date").asText();
                            orderItemCheckService.checkAge(packetResponse.packetResponse().getData().getProduct(), ndg, LocalDateTime.now(), Utility.convertToDateFormat((birthDate)));
                        }
                    }
                }     if(!entity.getOrderItem().isEmpty() && entity.getOrderItem().get(0).getInsured_item().get("insurance_holders_attributes") != null && !entity.getOrderItem().get(0).getInsured_item().get("insurance_holders_attributes").isEmpty()){
                    JsonNode insuranceHolderBirthDate = entity.getOrderItem().get(0).getInsured_item()
                            .get("insurance_holders_attributes").get(0).get("birth_date");
                    if(!insuranceHolderBirthDate.isNull() && !insuranceHolderBirthDate.asText().isEmpty()) {
                        String birthDate = entity.getOrderItem().get(0).getInsured_item().get("insurance_holders_attributes")
                                .get(0).get("birth_date").asText();
                        orderItemCheckService.checkAge(packetResponse.packetResponse().getData().getProduct(), ndg, LocalDateTime.now(),  Utility.convertToDateFormat((birthDate)));
                    }
                }
            }
        }

        // Validate company data if present
        if (entity.getOrderItem() != null && !entity.getOrderItem().isEmpty() && 
            entity.getOrderItem().get(0).getInsured_item() != null && 
            entity.getOrderItem().get(0).getInsured_item().has("company")) {

            Set<String> groups = jwtUtilsService.extractGroupsCognito();
            if(groups.contains("anonimo") || groups.contains("intermediary-anonimo")) {
                logger.info("Token anonimo o intermediary-anonimo, skippo validazione company.");
            } else {
                // Get customer data
                JsonNode customerData = serviceClientCustomer.findById(resultDb.getCustomerId(), token);
                // Get company data from insured_item
                JsonNode companyData = entity.getOrderItem().get(0).getInsured_item().get("company");

                if (companyData != null && customerData != null) {
                    // Validate company data using the OrderItemCheckService
                    orderItemCheckService.validateCompanyData(order_code, companyData, customerData, token);
                } else {
                    logger.warn("Company data or customer data is null, skipping company validation.");
                }
            }
        }

        AnagStatesEntity stateUpdating = stateRepo.find("state", entity.getAnagStates().getState()).firstResult();
        resultDb.setAnagStates(stateUpdating);
        List<OrderItemEntity> resultOrderItem=updateOrderItem(resultDb, entity,packetResponse,"", token);
        if(resultOrderItem!=null && resultOrderItem.get(0).getPrice()!=null){
            resultDb.setInsurancePremium(BigDecimal.valueOf(Double.valueOf(resultOrderItem.get(0).getPrice())));
        }
        OrderEntity orderUpdated = repo.getEntityManager().merge(resultDb);
        orderUpdated.setOrderItem(resultOrderItem);
        orderHistoryService.createHistoryForOrderPut(orderUpdated, entity.getChosenWarranties());
        orderUpdated.setStepState(commonUtils.getStepState(entity.getProductId()));
        return orderUpdated;
    }

    public OrderEntity putFromYin(String orderCode, String xTenantLanguage, OrderEntity entity, String token) throws Exception {
        List<JsonString> groupsJson=jsonWebToken.getClaim("cognito:groups");
        List<String> groups=new ArrayList<>();
        for (JsonString group : groupsJson) {
            groups.add(group.getString());
        }
        if(!groups.contains(technicalUser) && !groups.contains(intermediaryUser)) {
            throw new WebApplicationException(Response.status(403).entity("Forbidden").build());
        }else{
            return this.updateOrder(orderCode, xTenantLanguage, entity, true, entity.getCustomerId(), token, null);
        }
    }

    @Transactional
    @WithSpan("OrderService.updateOrder")
    public OrderEntity confirmOrder(
            @SpanAttribute("arg.id") String order_code) throws ProductClientException, ValidationStructureProductException, PacketException {

        OrderEntity resultDb = repo.find("order_code", order_code).firstResult();
        if(resultDb == null){
            throw new EntityNotFoundException(" Entity order by order_code=%d not found, id ", order_code);
        }
        OrderItemEntity orderItem = itemRepo.find("order_id", resultDb.getId()).firstResult();

        AnagStatesEntity stateUpdating = stateRepo.findByIdOptional(OrderStateEnum.CONFIRMED.getValue().longValue()).orElseThrow(() -> new EntityNotFoundException(String.format(
                "Entity order by id=%d not found", order_code)));

        resultDb.setAnagStates(stateUpdating);
        OrderEntity order = repo.getEntityManager().merge(resultDb);
        //updateOrderItem(order, null ,null, null);
        orderHistoryService.createHistoryForOrderPut(order, null);
        return order;
    }

    @WithSpan("OrderService.listOrders")
    public List<OrderEntity> listOrders() throws Exception {
        String ndg=jsonWebToken.getClaim("username");
        JsonNode customer=serviceClientCustomer.findByNdg(ndg);
        Integer customerId=customer.get("data").get("id").asInt();
        return repo.find("customerId", customerId).list();
    }

    @Transactional
    @WithSpan("OrderService.setLegacyRequest")
    public OrderEntity setLegacyRequest(JsonNode req, String orderCode) throws Exception {
        OrderEntity order=repo.find("order_code", orderCode).singleResult();
        if(order.getOrderItem().get(0).getInstance()==null){
            order.getOrderItem().get(0).setInstance(JsonNodeFactory.instance.objectNode());
        }
        ((ObjectNode)order.getOrderItem().get(0).getInstance()).put("orderManager", req);
        return repo.getEntityManager().merge(order);
    }

    @Transactional
    private List<OrderItemEntity> updateOrderItem(OrderEntity updatedEntity, OrderEntity reqEntity, Packet packetResponse, String discount, String token) throws Exception {
        Product product=packetResponse.packetResponse().getData().getProduct();
        List<OrderItemEntity> orderItemEntities = itemRepo.find("order_id", updatedEntity.getId()).list();
        JsonNode insuredItem = checkInsuredItem(orderItemEntities, reqEntity, packetResponse);
        CalculateCeilingsResponse ciel=Utility.calculateCeilings(reqEntity.getOrderItem(), insuredItem, packetResponse.packetResponse());
        orderItemEntities = checkInstance(orderItemEntities, reqEntity, token, ciel);
        orderItemEntities = checkStartDate(orderItemEntities, packetResponse, reqEntity);
        if(reqEntity.getPaymentFrequency()!=null){
            orderItemEntities.forEach(item->{
                if(item.getInstance()==null){
                    item.setInstance(JsonNodeFactory.instance.objectNode());
                }
                ((ObjectNode)item.getInstance()).put("paymentFrequency", reqEntity.getPaymentFrequency());
            });
        }
        //GESTIONE PER LE VARIANTI CHE INIZIALMENTE VENGONO CREATE CON IL PRODUCT/PACKET BASE
        // AGGIORNAMENTO DEL PRODUCT ID
        //AGGIORNAMENTO DEL PRICE SE DURANTE IL FUNNEL VIENE SCELTO UN PRODUCT/PACKET DIVERSO
        orderItemEntities.forEach(orderItemEntity->{
            reqEntity.getOrderItem().forEach(req ->{
                if(orderItemEntity.getPacketId()!=updatedEntity.getPacketId()){
                    orderItemEntity.setPacketId(updatedEntity.getPacketId());
                    logger.info("Setting price for orderItem " + orderItemEntity.getId()
                            + " from the packet response, because a different product/packet was chosen");
                    orderItemEntity.setPrice(String.valueOf(packetResponse.packetResponse().getData().getPacketPremium()));
                }
            });
        });

        // aggiornamento del product id nella casistica multiprodotto
        orderItemEntities.forEach(orderItemEntity->{
            reqEntity.getOrderItem().forEach(req ->{
                if(orderItemEntity.getProduct_id()!=updatedEntity.getProductId()){
                    orderItemEntity.setProduct_id(updatedEntity.getProductId());
                }
            });
        });

        orderItemEntities.forEach(orderItemEntity->{
            reqEntity.getOrderItem().forEach(req ->{
                if(orderItemEntity.getQuantity() == null && req.getQuantity() != null){
                    orderItemEntity.setQuantity(req.getQuantity());
                    logger.info("Setting quantity for orderItem " + orderItemEntity.getId());
                }
            });
        });

        //SE IL PREZZO RISULTA NULL ALLORA SETTO IL PACKET PREMIUM
        orderItemEntities.forEach(orderItemEntity -> {
            if(orderItemEntity.getPrice().isEmpty()){
                logger.info("Empty price for orderItem " + orderItemEntity.getId() + ". Setting price to packet premium");
                orderItemEntity.setPrice(String.valueOf(packetResponse.packetResponse().getData().getPacketPremium()));
            }
        });

        if(!reqEntity.getOrderItem().isEmpty() && reqEntity.getOrderItem().get(ORDER_ITEM).getQuantity()!=null){
            orderItemEntities.forEach(orderItemEntity -> orderItemEntity.setQuantity(reqEntity.getOrderItem().get(ORDER_ITEM).getQuantity()));
        }

        //SE IN RICHIESTA MI ARRIVA I PREZZO
        if(!reqEntity.getOrderItem().isEmpty() && reqEntity.getOrderItem().stream().filter(r ->r.getPrice()!=null).count()>0){
            List<OrderItemEntity> itemByPrice=reqEntity.getOrderItem().stream().filter(req ->req.getPrice()!=null).collect(Collectors.toList());
            logger.info("Setting orderItems' price based on the prices coming from request");
            orderItemEntities.forEach(price -> {
                itemByPrice.forEach(iByPrice -> {
                    price.setPrice(iByPrice.getPrice());
                });
            });
        }

        //paracadute per momentanea scontistica
        orderItemEntities.forEach(orderItemEntity ->{
            if(discount !=null && discount.equalsIgnoreCase("sconto1@@")){
                Integer discountPercentage=100;
                Float price=Float.parseFloat(orderItemEntity.getPrice());
                logger.info("Setting discounted price to orderItem " + orderItemEntity.getId());
                orderItemEntity.setPrice(String.valueOf(price-((price/100)*discountPercentage)));
            }
        });


        //salvataggio informazioni relavite alla quotation
        if(!reqEntity.getOrderItem().isEmpty() && reqEntity.getOrderItem().stream().filter(quotation->quotation.getQuotation()!=null).count()>0){
            List<OrderItemEntity> itemByQuotation=reqEntity.getOrderItem().stream().filter(quotation->quotation.getQuotation()!=null).collect(Collectors.toList());
            for (OrderItemEntity orderItemByQuotation: itemByQuotation) {
                for (OrderItemEntity orderItemEntity:orderItemEntities) {
                    orderItemEntity.setQuotation(orderItemByQuotation.getQuotation());
                }
            }
        }
        orderItemEntities.forEach(itemIsured ->{
            itemIsured.setInsured_item(insuredItem);
            itemRepo.getEntityManager().merge(itemIsured);
        });

        // se il prodotto non passa dal pricing abbiamo bisogno che instance venga inizializzato per l'emissione
        orderItemEntities.forEach(orderItemEntity -> {
            if(orderItemEntity.getInstance() == null){
                logger.info("Setting empty instance properties for orderItem: " + orderItemEntity.getId());
                orderItemEntity.setInstance(JsonNodeFactory.instance.objectNode());
            }
        });


        // If chosenWarranties.warranties is empty, price must be 0
        // request from BE - 25-10-2023
        JsonNode warrantiesNode = reqEntity.getChosenWarranties() == null
                ? null
                : reqEntity.getChosenWarranties().findValue("warranties");
        if (warrantiesNode != null && warrantiesNode.isEmpty()) {
            logger.info("Setting orderItems' price to 0 because chosenWarranties -> data -> warranties is empty");
            orderItemEntities.forEach(price -> price.setPrice("0"));
        }

        if(!reqEntity.getOrderItem().isEmpty() &&
                product.getConfiguration().getProperties().hasNonNull("ageSeparation") &&
                !reqEntity.getOrderItem().get(0).getInsured_item().withArray("insurance_holders").isEmpty()){
            JsonNode ageSeparation=product.getConfiguration().getProperties().get("ageSeparation");
            long age=ageSeparation.get("age").asLong();
            String uOString=ageSeparation.hasNonNull("underOver") ? ageSeparation.get("underOver").
                    asText() : Long.toString(age);
            ArrayNode under=JsonNodeFactory.instance.arrayNode(),over=JsonNodeFactory.instance.arrayNode();
            OrderItemEntity reqOrderItem=reqEntity.getOrderItem().get(0);
            if(reqOrderItem.getInsured_item().hasNonNull("insurance_holders")
                && !reqOrderItem.getInsured_item().withArray("insurance_holders").isEmpty()){
                for(JsonNode insured : reqOrderItem.getInsured_item().withArray("insurance_holders")){
                    LocalDate insuredBirth=LocalDate.parse(insured.get("birth_date").asText());
                    if (insuredBirth.plusYears(age).isAfter(LocalDate.now())) {
                        under.add(insured);
                    } else {
                        over.add(insured);
                    }
                }
                if (reqOrderItem.getInsured_item().get("under"+uOString).asInt()!=under.size() ||
                    reqOrderItem.getInsured_item().get("over"+uOString).asInt()!=over.size()){
                    throw new OrderEx("Numero assicurati under/over non corretto. Assicurati ricevuti da fe(U-O): "+reqOrderItem.getInsured_item().get("under"+uOString).asInt()
                    +"-"+reqOrderItem.getInsured_item().get("over"+uOString).asInt()+". Assicurati calcolati dal be(U-O): "+under.size()+"-"+over.size());
                }
            }
        }

        orderItemCheckService.checkOrderItemsEntities(product, reqEntity, token ,orderItemEntities.get(ORDER_ITEM).getStart_date());


        return orderItemEntities;
    }

    @Transactional
    @WithSpan("OrderService.updateOrder")
    public OrderEntity updateOrderQuotation(
            @SpanAttribute("arg.id") String order_code,
            @SpanAttribute("arg.quotation") JsonNode quotation) throws Exception {
        OrderEntity order = repo.find("order_code", order_code).firstResult();
        OrderItemEntity orderItem = order.getOrderItem().get(0);

        if (orderItem.getInstance() == null) {
            orderItem.setInstance(JsonNodeFactory.instance.objectNode());
        }

        ((ObjectNode) orderItem.getInstance()).set("quotation", quotation);
        JsonNode dataNode = quotation.get("data");

        if (dataNode.isObject()) {
            ObjectNode quotationNode = (ObjectNode) orderItem.getInstance().get("quotation");
            quotationNode.set("data", dataNode);
        } else if (dataNode != null && dataNode.isArray() && dataNode.size() == 1) {
            // If discountedTotal is not null, use it as order price
            JsonNode price = dataNode.get(0).get("discountedTotal");
            if (price == null || price.isNull()) {
                price = dataNode.get(0).get("total");
            }
            String stringPrice = orderItem.getInstance().has("yin_commission") ?
                    String.valueOf(price.asDouble() + orderItem.getInstance().get("yin_commission").asDouble()) :
                    price.asText();
            orderItem.setPrice(stringPrice);
            if (dataNode.get(0).has("annualTotal") && !dataNode.get(0).get("annualTotal").isNull()) {
                orderItem.setAnnualPrice(dataNode.get(0).get("annualTotal").asText());
            }
        } else {
            // TODO
        }
        if(dataNode != null && dataNode.isArray() && quotation.withArray("data").get(0).hasNonNull("cieling")){
            order.getOrderItem().get(0).getInstance().get("chosenWarranties").get("data")
                    .withArray("warranties").spliterator().forEachRemaining(j->{
                        if(j.get("anagWarranty").get("name").asText().equalsIgnoreCase(
                                quotation.withArray("data").get(0).get("cielingWarranty").asText())){
                            ((ArrayNode)j.get("ceilings").withArray("ceilings"))
                                    .add(quotation.withArray("data").get(0).get("cieling").asLong());
                        }
                    });
        }
        order.setInsurancePremium(BigDecimal.valueOf(Double.valueOf(order.getOrderItem().get(0).getPrice())));
        return repo.getEntityManager().merge(order);
    }

    @WithSpan("OrderService.listEstimates")
    public List<OrderEntity> listEstimates(String token) throws Exception {


        String ndg=jsonWebToken.getClaim("username");
        JsonNode customer=serviceClientCustomer.findByNdg(ndg);
        Integer customerId=customer.get("data").get("id").asInt();
        Map<String, Object> params = new HashMap<>();
        AnagStatesEntity state=stateRepo.find("state", StringUtils.capitalize(STATE_ORDER_ESTIMATE)).firstResult();

        params.put("customer_id", customerId);
        params.put("anag_state_id", state.getId());
        params.put("created_at", LocalDateTime.now().minusDays(30));


        List<OrderEntity> orders=repo.find(" customer_id = :customer_id and anag_state_id = :anag_state_id and created_at > :created_at", params).list();

        orders.forEach(item->{
            ProductResponse productResponse = invokeProductByProductId(Long.valueOf(item.getProductId()), token);
            logger.info("Product code:"+productResponse.getData().getCode());
            item.setProductCode(productResponse.getData().getCode());
        });

        return  orders;
    }
    @WithSpan("OrderService.duplicatedOrder")
    @Transactional
    public OrderEntity duplicatedOrder(
            @SpanAttribute("arg.orderEntity") OrderEntity orderEntity, 
            String token, 
            String sessionId) throws Exception {

        // Input validation
        if (orderEntity == null || StringUtils.isBlank(orderEntity.getOrderCode())) {
            throw new IllegalArgumentException("OrderEntity or orderCode cannot be null or empty");
        }
        if (StringUtils.isBlank(token)) {
            throw new IllegalArgumentException("Token cannot be null or empty");
        }

        logger.info("OrderService.duplicatedOrder() start with entity: " + orderEntity.getOrderCode());
        
        try {
            // Find parent order
            OrderEntity parent = repo.find("order_code", orderEntity.getOrderCode()).firstResult();
            if (parent == null) {
                throw new EntityNotFoundException("Parent order not found with orderCode: " + orderEntity.getOrderCode());
            }

            // Validate session for the parent order
            checkSessionId(parent, sessionId);
            
            Long parentId = parent.getId();
            List<OrderItemEntity> orderItems = parent.getOrderItem();
            
            if (orderItems == null || orderItems.isEmpty()) {
                throw new IllegalStateException("Parent order has no order items");
            }

            // Generate new order code
            String orderCode = Utility.generateOrderCode();
            
            // Detach entities to prepare for duplication
            repo.getEntityManager().detach(parent);
            parent.setOrderItem(null);
            orderItems.forEach(orderItem -> {
                itemRepo.getEntityManager().detach(orderItem);
            });

            // Setup parent order chain
            String parentCode = parent.getOrderCode();
            if (parent.getParentOrder() != null) {
                parentCode = parent.getParentOrder() + ";" + parentCode;
            }

            // Configure duplicated order
            parent.setId(null);
            parent.setParentOrder(parentCode);
            parent.setOrderCode(orderCode);
            parent.setAnagStates(stateRepo.find("state", STATE_ORDER_CREATE).firstResult());
            
            // Apply modern patterns for session and customer management
            assignSessionIdToOrder(parent, sessionId);
            assignCustomerToOrder(parent);
            
            // Persist the duplicated order
            repo.persist(parent);
            
            // Get packet response and policy information
            Packet packetResponse = checkPacketFromOrderItem(parent, parent, token);
            PolicyResponseDto policy = policyClient.readPolicyByOrderId(token, parentId)
                    .readEntity(PolicyResponseDto.class);
            
            if (policy == null || policy.getData() == null || policy.getData().getEndDate() == null) {
                throw new IllegalStateException("Unable to retrieve valid policy information for duplication");
            }

            // Process order items with modern duration calculation
            orderItems.forEach(orderItem -> {
                // Set state and dates
                orderItem.setState(INSURANCE);
                orderItem.setStart_date(policy.getData().getEndDate().plusDays(1).toLocalDate().atStartOfDay());
                
                // Use modern getOrderDuration instead of getOrderDurationV2
                if (packetResponse.packetResponse().getData().getPacketDuration() != null &&
                    packetResponse.packetResponse().getData().getPacketDuration().get("packet_duration") != null) {
                    
                    JsonNode packetDuration = packetResponse.packetResponse().getData()
                            .getPacketDuration().get("packet_duration").get(0);
                    
                    orderItem.setExpiration_date(Utility.getOrderDuration(
                            orderItem.getStart_date(),
                            packetDuration.get("duration").asInt(),
                            packetDuration.get("durationType").asText()));
                } else {
                    logger.warn("No packet duration found, using default duration calculation");
                    orderItem.setExpiration_date(orderItem.getStart_date().plusYears(1));
                }

                // Set chosen warranties from original order
                if (orderEntity.getOrderItem() != null && 
                    !orderEntity.getOrderItem().isEmpty() && 
                    orderEntity.getOrderItem().get(0).getInstance() != null &&
                    orderEntity.getOrderItem().get(0).getInstance().has("chosenWarranties")) {
                    
                    ((ObjectNode) orderItem.getInstance()).set(
                            "chosenWarranties", 
                            orderEntity.getOrderItem().get(0).getInstance().get("chosenWarranties"));
                }

                // Reset ID and link to new parent
                orderItem.setId(null);
                orderItem.setOrders(parent);
            });

            // Process insured items and calculate ceilings
            JsonNode insuredItem = checkInsuredItem(orderItems, parent, packetResponse);
            CalculateCeilingsResponse ciel = Utility.calculateCeilings(orderItems, insuredItem, 
                    packetResponse.packetResponse());
            
            // Check instances with updated logic
            orderItems = checkInstance(orderItems, parent, token, ciel);
            
            // Persist order items
            orderItems.forEach(orderItem -> itemRepo.persist(orderItem));
            
            // Create history record
            orderHistoryService.createHistoryForOrderInsert(parent);
            
            // Set step state following modern pattern
            parent.setStepState(commonUtils.getStepState(parent.getProductId()));
            
            // Set order items and return
            parent.setOrderItem(orderItems);
            
            logger.info("OrderService.duplicatedOrder() completed successfully for orderCode: " + orderCode);
            return parent;
              } catch (EntityNotFoundException e) {
            logger.error("Entity not found during order duplication: " + e.getMessage());
            throw e;
        } catch (IllegalArgumentException | IllegalStateException e) {
            logger.error("Validation error during order duplication: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error during order duplication for orderCode " + 
                    orderEntity.getOrderCode() + ": " + e.getMessage(), e);
            throw new RuntimeException("Failed to duplicate order: " + e.getMessage(), e);
        }
    }

    @WithSpan("OrderService.readOrdersByCustomer")
    @Transactional
    public List<String> readOrdersByCustomer(
            @SpanAttribute("arg.orderEntity") Long customerId) throws Exception {

        logger.info("OrderService.readOrdersByCustomer() start with customer " + customerId);
        List<OrderEntity> orders=repo.find("customer_id", customerId).list();
        logger.info("OrderService.readOrdersByCustomer() end");
        return orders.stream().map(OrderEntity::getOrderCode).toList();
    }

    public void computeAnswerRules(OrderBoundaryResponse order) {
        AtomicReference<ArrayNode> warrantiesRef = new AtomicReference<>(null);
        AtomicReference<Set<String>> warrantyCodesRef = new AtomicReference<>(null);
        order.getProduct().getData().getQuestions().forEach(question -> {
            question.getAnswers().forEach(answer ->
            {
                if (!answer.get("rule").isTextual()) {
                    if (answer.get("rule").has("warrantiesConditions")) {
                        JsonNode warrantiesConditionsNode = answer.get("rule").path("warrantiesConditions");
                        if (warrantiesRef.get() == null) {
                            ArrayNode warranties = order.getOrderItem().get(0).getInstance().at("/chosenWarranties/data").withArray("warranties");
                            warrantiesRef.set(warranties);
                            Set<String> warrantyCodes = StreamSupport.stream(warranties.spliterator(), false)
                                    .map(warranty -> warranty.at("/anagWarranty/internal_code").asText())
                                    .collect(Collectors.toSet());
                            warrantyCodesRef.set(warrantyCodes);
                        }
                        Set<String> warrantyCodes = warrantyCodesRef.get();
                        if (warrantiesConditionsNode.has("presence")) {
                            ArrayNode requiredWarrantiesCodes = warrantiesConditionsNode.withArray("presence");
                            Set<String> requiredCodes = StreamSupport.stream(requiredWarrantiesCodes.spliterator(), false)
                                    .map(JsonNode::asText)
                                    .collect(Collectors.toSet());
                            if (warrantyCodes.containsAll(requiredCodes)) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (warrantiesConditionsNode.has("absence")) {
                            ArrayNode absentWarrantiesCodes = warrantiesConditionsNode.withArray("absence");
                            Set<String> absentCodes = StreamSupport.stream(absentWarrantiesCodes.spliterator(), false)
                                    .map(JsonNode::asText)
                                    .collect(Collectors.toSet());
                            if (!Collections.disjoint(warrantyCodes, absentCodes)) {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            } else {
                                ((ObjectNode) answer).putNull("rule");
                            }
                        }
                    } else if (answer.get("rule").has("priceConditions")) {
                        double price = Double.parseDouble(order.getOrderItem().get(0).getPrice());
                        double annualPrice = 0;
                        if (order.getOrderItem().get(0).getAnnualPrice() != null) {
                            annualPrice = Double.parseDouble(order.getOrderItem().get(0).getAnnualPrice());
                        }
                        JsonNode priceConditionsNode = answer.get("rule").path("priceConditions");

                        // Conditions for monthly price
                        if (priceConditionsNode.has("less")) {
                            if (price < priceConditionsNode.get("less").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("lessOrEqual")) {
                            if (price <= priceConditionsNode.get("lessOrEqual").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("equal")) {
                            if (price == priceConditionsNode.get("equal").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("greaterOrEqual")) {
                            if (price >= priceConditionsNode.get("greaterOrEqual").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("greater")) {
                            if (price > priceConditionsNode.get("greater").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        // Conditions for annual Price
                        if (priceConditionsNode.has("annualLess")) {
                            if (annualPrice < priceConditionsNode.get("annualLess").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("annualLessOrEqual")) {
                            if (annualPrice <= priceConditionsNode.get("annualLessOrEqual").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("annualEqual")) {
                            if (annualPrice == priceConditionsNode.get("annualEqual").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("annualGreaterOrEqual")) {
                            if (annualPrice >= priceConditionsNode.get("annualGreaterOrEqual").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("annualGreater")) {
                            if (annualPrice > priceConditionsNode.get("annualGreater").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                    } else if (answer.get("rule").has("prevent_checkout")) {
                        ((ObjectNode) answer).put("rule", "prevent_checkout");
                    } else {
                        ((ObjectNode) answer).putNull("rule");
                    }
                }
            });
        });
    }

    public PacketResponse computeAnswerRules(PacketResponse packet, OrderBoundaryResponse order) {
        AtomicReference<ArrayNode> warrantiesRef = new AtomicReference<>(null);
        AtomicReference<Set<String>> warrantyCodesRef = new AtomicReference<>(null);
        packet.getData().getQuestions().forEach(question -> {
            question.getAnswers().forEach(answer ->
            {
                if (!answer.get("rule").isTextual()) {
                    if (answer.get("rule").has("warrantiesConditions")) {
                        JsonNode warrantiesConditionsNode = answer.get("rule").path("warrantiesConditions");
                        if (warrantiesRef.get() == null) {
                            ArrayNode warranties = order.getOrderItem().get(0).getInstance().at("/chosenWarranties/data").withArray("warranties");
                            warrantiesRef.set(warranties);
                            Set<String> warrantyCodes = StreamSupport.stream(warranties.spliterator(), false)
                                    .map(warranty -> warranty.at("/anagWarranty/internal_code").asText())
                                    .collect(Collectors.toSet());
                            warrantyCodesRef.set(warrantyCodes);
                        }
                        Set<String> warrantyCodes = warrantyCodesRef.get();
                        if (warrantiesConditionsNode.has("presence")) {
                            ArrayNode requiredWarrantiesCodes = warrantiesConditionsNode.withArray("presence");
                            Set<String> requiredCodes = StreamSupport.stream(requiredWarrantiesCodes.spliterator(), false)
                                    .map(JsonNode::asText)
                                    .collect(Collectors.toSet());
                            if (warrantyCodes.containsAll(requiredCodes)) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (warrantiesConditionsNode.has("absence")) {
                            ArrayNode absentWarrantiesCodes = warrantiesConditionsNode.withArray("absence");
                            Set<String> absentCodes = StreamSupport.stream(absentWarrantiesCodes.spliterator(), false)
                                    .map(JsonNode::asText)
                                    .collect(Collectors.toSet());
                            if (!Collections.disjoint(warrantyCodes, absentCodes)) {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            } else {
                                ((ObjectNode) answer).putNull("rule");
                            }
                        }
                    } else if (answer.get("rule").has("priceConditions")) {
                        double price = Double.parseDouble(order.getOrderItem().get(0).getPrice());
                        double annualPrice = 0;
                        if (order.getOrderItem().get(0).getAnnualPrice() != null) {
                            annualPrice = Double.parseDouble(order.getOrderItem().get(0).getAnnualPrice());
                        }
                        // set monthly price
                        JsonNode priceConditionsNode = answer.get("rule").path("priceConditions");
                        if (priceConditionsNode.has("less")) {
                            if (price < priceConditionsNode.get("less").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("lessOrEqual")) {
                            if (price <= priceConditionsNode.get("lessOrEqual").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("equal")) {
                            if (price == priceConditionsNode.get("equal").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("greaterOrEqual")) {
                            if (price >= priceConditionsNode.get("greaterOrEqual").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("greater")) {
                            if (price > priceConditionsNode.get("greater").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        // set annual price
                        if (priceConditionsNode.has("annualLess")) {
                            if (annualPrice < priceConditionsNode.get("annualLess").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("annualLessOrEqual")) {
                            if (annualPrice <= priceConditionsNode.get("annualLessOrEqual").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("annualEqual")) {
                            if (annualPrice == priceConditionsNode.get("annualEqual").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("annualGreaterOrEqual")) {
                            if (annualPrice >= priceConditionsNode.get("annualGreaterOrEqual").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                        if (priceConditionsNode.has("annualGreater")) {
                            if (annualPrice > priceConditionsNode.get("annualGreater").asDouble()) {
                                ((ObjectNode) answer).putNull("rule");
                            } else {
                                ((ObjectNode) answer).put("rule", "prevent_checkout");
                            }
                        }
                    } else if (answer.get("rule").has("prevent_checkout")) {
                        ((ObjectNode) answer).put("rule", "prevent_checkout");
                    } else {
                        ((ObjectNode) answer).putNull("rule");
                    }
                }
            });
        });
        return packet;
    }



    private Catalog invokeCatalogByProductCode(String product_code, JsonNode asset) throws CatalogClientException, ValidationStructureProductException {
        Catalog catalogResponse = serviceClientCatalog.findByProductCode(product_code);
        if(catalogResponse.enabled()){
            Utility.validationProductStructure(catalogResponse.goodsStructure(), asset);
        }

        return catalogResponse;
    }

    /*
     * CHIAMATA A V3 PRODUCT
     */
    private ProductResponse invokeProductByProductId(Long id, String token) throws ProductClientException {
        ProductResponse productResponse = serviceClientProduct.getById(id, token);
        return productResponse;
    }

    private JsonNode invokeProductByProductIdJSON(Long id, String token) throws ProductClientException {
        JsonNode productResponse = serviceClientProduct.getByIdJSON(id, token);
        return productResponse;
    }

    /*
     * CHIAMATA A V3 PACKET
     */
    private Packet invokePacketByPacketId(Long id, String token) throws PacketException {
        Packet packetResponse = serviceClientPacket.findById(id, token);
        return packetResponse;
    }

    private Packet invokePacketByTipologia(TipologiaRequest request, String token) throws PacketException {
        Packet packetResponse = serviceClientPacket.findByTipologia(request, token);
        return packetResponse;
    }



    private List<OrderItemEntity> checkInstance(List<OrderItemEntity> orderItems, OrderEntity entity, String token,
                                 CalculateCeilingsResponse ceiling) throws Exception {
        JsonNode warranties=entity.getChosenWarranties();
        JsonNode resPg=entity.getResPg();
        if(warranties!=null && !(warranties.hasNonNull("skipRules") && warranties.get("skipRules").asBoolean())) {
            if (warranties!=null && warranties.hasNonNull("skipRules")){
                ((ObjectNode)warranties).remove("skipRules");
            }
            try{
                JsonNode responseWarranties=warrantyRulesClient.valideteWarranties(warranties, token).readEntity(JsonNode.class);
            }catch (WarrantyRulesClientException e){
                throw new WebApplicationException(Response.status(400).entity(new ValidationWarrantiesResponse(e.getMessage(), e.getBody())).build());
            }
            for (OrderItemEntity orderItem : orderItems) {
                if (orderItem.getInstance() == null) {
                    orderItem.setInstance(JsonNodeFactory.instance.objectNode());
                }
                ((ObjectNode) orderItem.getInstance()).set("chosenWarranties", warranties);
            }
        }
        if (resPg != null) {
            for (OrderItemEntity orderItem : orderItems) {
                if (orderItem.getInstance() == null) {
                    orderItem.setInstance(JsonNodeFactory.instance.objectNode());
                }
                ((ObjectNode) orderItem.getInstance()).set("pgResponse", resPg);
            }
        }
        if (ceiling.isUpdated()) {
            for (OrderItemEntity orderItem : orderItems) {
                if (orderItem.getInstance() == null) {
                    orderItem.setInstance(JsonNodeFactory.instance.objectNode());
                }
                ((ObjectNode) orderItem.getInstance()).set("ceilings", ceiling.getWarranties());
            }
        }

        return orderItems;
    }



    private JsonNode checkInsuredItem(List<OrderItemEntity> orderItemEntities, OrderEntity entity, Packet packetResponse){
        JsonNode insuredItem = null;
        for (OrderItemEntity orderItem : orderItemEntities) {
            if (entity.getOrderItem() != null && !entity.getOrderItem().isEmpty()) {
                if (packetResponse.packetResponse().getData().getProduct().getConfiguration().getProperties().hasNonNull("defaultDestination")){
                    ((ObjectNode)entity.getOrderItem().get(ORDER_ITEM).getInsured_item()).put("destination", packetResponse.packetResponse().getData().getProduct().
                            getConfiguration().getProperties().get("defaultDestination").asText());
                }
                insuredItem = entity.getOrderItem().get(ORDER_ITEM).getInsured_item();
                Catalog catalogResponse = invokeCatalogByProductCode(packetResponse.packetResponse().getData().getProduct().getCode(), insuredItem);
            } else if (orderItem.getInsured_item() != null) {
                insuredItem = orderItem.getInsured_item();
            }
        }
        if (packetResponse.packetResponse().getData().getProduct().getConfiguration().getProperties().hasNonNull("defaultDestination")){
            ((ObjectNode)insuredItem).put("destination", packetResponse.packetResponse().getData().getProduct().
                    getConfiguration().getProperties().get("defaultDestination").asText());
        }
        return insuredItem;
    }


    private List<OrderItemEntity> checkStartDate(List<OrderItemEntity> orderItems, Packet packetResponse, OrderEntity entity){
        if(!entity.getOrderItem().isEmpty() && entity.getOrderItem().get(ORDER_ITEM).getStart_date()==null && entity.getOrderItem().get(ORDER_ITEM).getInsured_item().get("start_date")!=null){
            entity.getOrderItem().get(ORDER_ITEM).setStart_date(OffsetDateTime.parse(entity.getOrderItem().get(ORDER_ITEM).getInsured_item().get("start_date").asText()).toLocalDateTime());
        }
        if(!entity.getOrderItem().isEmpty() && entity.getOrderItem().get(ORDER_ITEM).getExpiration_date()==null && entity.getOrderItem().get(ORDER_ITEM).getInsured_item().get("expiration_date")!=null){
            entity.getOrderItem().get(ORDER_ITEM).setExpiration_date(OffsetDateTime.parse(entity.getOrderItem().get(ORDER_ITEM).getInsured_item().get("expiration_date").asText()).toLocalDateTime());
        } else if(entity.getOrderItem()!=null && !entity.getOrderItem().isEmpty() &&
                packetResponse.packetResponse().getData().getConfiguration().hasNonNull("instant") &&
                packetResponse.packetResponse().getData().getConfiguration().get("instant").booleanValue()){
            entity.getOrderItem().get(0).setStart_date(LocalDateTime.now());
        }
        for(OrderItemEntity orderItem : orderItems) {
            if (!entity.getOrderItem().isEmpty() && entity.getOrderItem().get(ORDER_ITEM).getStart_date() != null && entity.getProductCode() != "tim-bill-protector" && entity.getProductCode() != "tim-bill-protection") {
                orderItem.setStart_date(entity.getOrderItem().get(ORDER_ITEM).getStart_date().with(ChronoField.MILLI_OF_SECOND, 1));
                if(entity.getOrderItem().get(0).getExpiration_date()!=null && packetResponse.packetResponse().getData().getSku().startsWith("tim-for-ski")) {
                    orderItem.setExpiration_date(entity.getOrderItem().get(0).getExpiration_date().plusDays(1).minus(1, ChronoUnit.MILLIS));
                }else if(entity.getOrderItem().get(0).getExpiration_date()!=null && packetResponse.packetResponse().getData().getSku().startsWith("helvetia-travel")){
                    orderItem.setExpiration_date(entity.getOrderItem().get(0).getExpiration_date().plusDays(1).minus(1, ChronoUnit.MILLIS));
                }else if(packetResponse.packetResponse().getData().getPacketDuration() !=null &&
                        packetResponse.packetResponse().getData().getPacketDuration().get("packet_duration") != null){
                    Integer duration=null;
                    String durationType=null;
                    LocalDate fixedEndDate=null;
                    if(StringUtils.isNotBlank(entity.getPacketDurationDescription())){
                        for(JsonNode packetDuration : packetResponse.packetResponse().getData().getPacketDuration().get("packet_duration")){
                            if(packetDuration.get("description").asText().equalsIgnoreCase(entity.getPacketDurationDescription())){
                                if(packetDuration.has("fixedEndDate")){
                                    fixedEndDate=LocalDate.parse(packetDuration.get("fixedEndDate").asText());
                                }else {
                                    duration = packetDuration.get("duration").asInt();
                                    durationType = packetDuration.get("durationType").asText();
                                }
                                if(orderItem.getInstance()==null){
                                    orderItem.setInstance(JsonNodeFactory.instance.objectNode());
                                }
                                ((ObjectNode)orderItem.getInstance()).set("packetDuration", packetDuration);
                                break;
                            }
                        }
                    }else {
                        JsonNode packetDuration = packetResponse.packetResponse().getData().getPacketDuration().get("packet_duration").get(0);
                        duration = packetDuration.get("duration").asInt();
                        durationType = packetDuration.get("durationType").asText();
                        if (orderItem.getInstance() != null && orderItem.getInstance().isObject()) {
                            ((ObjectNode)orderItem.getInstance()).set("packetDuration", packetDuration);
                        }
                    }
                    if(fixedEndDate!=null){
                        orderItem.setExpiration_date(fixedEndDate.plusDays(1).atStartOfDay().minus(1, ChronoUnit.MILLIS));
                    }else {
                        orderItem.setExpiration_date(Utility.getOrderDuration(
                                orderItem.getStart_date(), duration, durationType).minusDays(1));
                    }
                } else if (packetResponse.packetResponse().getData().getProduct().getConfiguration().getProperties().hasNonNull("durationFromDays") &&
                        packetResponse.packetResponse().getData().getProduct().getConfiguration().getProperties().get("durationFromDays").asBoolean()) {
                    orderItem.setStart_date(entity.getOrderItem().get(ORDER_ITEM).getStart_date().toLocalDate().atStartOfDay());
                    orderItem.setExpiration_date(orderItem.getStart_date().plusDays(entity.getOrderItem().get(ORDER_ITEM).getDays()).minus(1, ChronoUnit.MILLIS));
                } else if (StringUtils.isNotBlank(packetResponse.packetResponse().getData().getDuration())
                        && StringUtils.isNotBlank(packetResponse.packetResponse().getData().getDurationType())) {
                    orderItem.setExpiration_date(
                            Utility.getOrderDuration(
                                    entity.getOrderItem().get(ORDER_ITEM).getStart_date(),
                                    Integer.parseInt(packetResponse.packetResponse().getData().getDuration()),
                                    packetResponse.packetResponse().getData().getDurationType()));
                }
            }
            else if(!entity.getOrderItem().isEmpty()){
                orderItem.setStart_date(LocalDateTime.now().toLocalDate().atStartOfDay().plusDays(1));
                orderItem.setExpiration_date(LocalDateTime.now().toLocalDate().plusYears(1).plusDays(1).atStartOfDay().minus(1, ChronoUnit.MILLIS));
            }
        }
        return orderItems;
    }


    @Transactional
    @WithSpan("OrderService.updateEmission")
    public OrderEntity updateEmission(
            @SpanAttribute("arg.id") String order_code, Long orderItemId, String token, JsonNode emission) throws ProductClientException, CatalogClientException, ValidationStructureProductException, PacketException {

        //recupero l'ordine a db
        OrderEntity resultDb = repo.find("order_code", order_code).firstResult();
        if (resultDb == null) {
            throw new EntityNotFoundException(" Entity order by order_code=%d not found, id ", order_code);
        }
        //recupero l'order item
        OrderItemEntity orderItemDb = itemRepo.find("order_id", resultDb.getId()).firstResult();
        //setto la emission
        orderItemDb.setEmission(emission);
        itemRepo.getEntityManager().merge(orderItemDb);

        OrderEntity orderAfterUpdate  = repo.getEntityManager().merge(resultDb);
        if(orderItemDb!=null){
            orderAfterUpdate.getOrderItem().set(0, orderItemDb);
        }
        return orderAfterUpdate;
    }

    @Transactional
    @WithSpan("OrderService.priceAdjustment")
    public void priceAdjustment(Double priceAdjustment, String orderCode) throws ProductClientException, CatalogClientException, ValidationStructureProductException, PacketException {
        if(!checkTokenTI()){
            throw new WebApplicationException(Response.status(403).entity("Forbidden").build());
        }
        //recupero l'ordine a db
        OrderEntity resultDb = repo.find("order_code", orderCode).firstResult();
        if (resultDb == null) {
            throw new EntityNotFoundException(" Entity order by order_code=%d not found, id ", orderCode);
        }
        //recupero l'order item
        OrderItemEntity orderItemDb = itemRepo.find("order_id", resultDb.getId()).firstResult();
        //setto adjustment
        Double price=Double.valueOf(orderItemDb.getPrice());
        price+=priceAdjustment;
        orderItemDb.setPrice(String.valueOf(price));
        resultDb.setInsurancePremium(BigDecimal.valueOf(price));
        repo.getEntityManager().merge(resultDb);
        ((ObjectNode)orderItemDb.getInstance()).put("yin_commission", priceAdjustment);
        itemRepo.getEntityManager().merge(orderItemDb);
    }

    @WithSpan("OrderService.listOrders")
    @Transactional
    public OrderEntity updateRetryStep(String orderCode, Integer step) throws Exception {
        OrderEntity order=repo.find("order_code", orderCode).firstResult();
        ((ObjectNode)order.getOrderItem().get(0).getInstance()).put("retryStep", step);
        return repo.getEntityManager().merge(order);
    }

    @WithSpan("OrderService.checkExistingPolicy")
    public String checkExistingPolicy(String orderCode, String token) throws Exception {
        OrderEntity order=repo.find("order_code", orderCode).firstResult();
        CheckExistingReq req=new CheckExistingReq();
        ProductResponse productResponse=serviceClientProduct.getById(Long.valueOf(order.getProductId()), token);
        JsonNode properties=productResponse.getData().getConfiguration().getProperties();
        List<Long> customerIds;
        if(properties.get("existingCheck").has("taxCodeAndEmail")) {
            customerIds = serviceClientCustomer.findByTaxCodeAndEmail(Long.valueOf(order.getCustomerId()), token);
        } else if (properties.get("existingCheck").has("taxCodeCheck")) {
            customerIds = serviceClientCustomer.findByTaxcode(Long.valueOf(order.getCustomerId()), token);
        } else {
            customerIds = Collections.singletonList(Long.valueOf(order.getCustomerId()));
        }
        req.setCustomerId(customerIds);
        req.setProductId(Long.valueOf(order.getProductId()));
        req.setPacketId(Long.valueOf(order.getPacketId()));
        req.setStart(order.getOrderItem().get(0).getStart_date());
        req.setEnd(order.getOrderItem().get(0).getExpiration_date());
        if(order.getOrderItem().get(0).getInsured_item()!=null &&
                order.getOrderItem().get(0).getInsured_item().has("destination")) {
            req.setDestination(order.getOrderItem().get(0).getInsured_item().get("destination").asText());
        }
        return policyClient.checkExistingPolicy(token, req).readEntity(JsonNode.class).get("data").asText();
    }


    @WithSpan("OrderService.getByPackets")
    @Transactional
    public List<Long> getByPackets(IdsDto req, String token) throws Exception {
        Map<String, Object> params=new HashMap<>();
        params.put("customer_id", req.getCustomerId());
        params.put("packet_id", req.getIds());
        return repo
                .find("customer_id = :customer_id AND packet_id IN :packet_id", params)
                .list().stream().map(OrderEntity::getId).collect(Collectors.toList());
    }

    public List<Long> getByProductDestination(IdsDto req, String token) throws Exception {
        Map<String, Object> params=new HashMap<>();
        params.put("customer_id", req.getCustomerId());
        params.put("product_id", req.getIds());
        return repo
                .find("customer_id = :customer_id AND product_id IN :product_id", params)
                .list().stream().filter(o->o.getOrderItem().get(0).getInsured_item().hasNonNull("destination") &&
                        o.getOrderItem().get(0).getInsured_item().get("destination").asText().equalsIgnoreCase(req.getDestination())).
                map(OrderEntity::getId).collect(Collectors.toList());
    }

    @WithSpan("OrderService.updateStartDate")
    @Transactional
    public OrderEntity updateStartDate(String orderCode) throws Exception {
        OrderEntity order=repo.find("order_code", orderCode).firstResult();
        OrderItemEntity orderItem=order.getOrderItem().get(0);
        if(orderItem.getStart_date().toLocalDate().isAfter(LocalDate.now())){
            return order;
        }
        long days=orderItem.getStart_date().toLocalDate().until(LocalDate.now(), ChronoUnit.DAYS);
        orderItem.setStart_date(LocalDate.now().plusDays(1).atStartOfDay());
        orderItem.setExpiration_date(orderItem.getExpiration_date().plusDays(days+1));
        itemRepo.getEntityManager().merge(orderItem);
        order.getOrderItem().set(0, orderItem);
        return repo.getEntityManager().merge(order);
    }


    private void checkOrderAndCustomer(OrderEntity order, String id) throws Exception {
        if(!checkTokenTI()) {
            //se non è tch o int controllo che il customer che ha effettuato la get sia lo stesso di quello dell'ordine
            String ndg = jsonWebToken.getClaim("username");
            JsonNode customer = serviceClientCustomer.findByNdg(ndg);
            JsonNode tchCustomer = serviceClientCustomer.findByNdg(tchUser);
            Integer customerId = customer.get("data").get("id").asInt();
            Integer tchCustomerId = tchCustomer.get("data").get("id").asInt();
            if (order == null || (order.getCustomerId().intValue() != customerId.intValue() &&
                    order.getCustomerId().intValue() != tchCustomerId.intValue())) {
                throw new EntityNotFoundException("Entity not found", String.format(
                        "Entity order not found by " + id));
            }
        }
    }

    /**
     * Check packets from order item and filter them based on business rules
     *
     * @param entity Order entity containing order data
     * @param resultDb Order entity to update with filtered packets
     * @param token Authorization token
     * @return List of filtered packets
     */
    @WithSpan("OrderService.checkPacketsFromOrderItem")
    private List<Packet> checkPacketsFromOrderItem(OrderEntity entity, OrderEntity resultDb, String token) {
        try {
            logger.info("Checking packets from order item for product ID: " + entity.getProductId());

            // Get packets by product ID
            List<Packet> packets = serviceClientPacket.findByProductId(Long.valueOf(entity.getProductId()), token);
            logger.info("Found " + packets.size() + " packets for product ID: " + entity.getProductId());

            // Filter packets based on business rules
            List<Packet> filteredPackets = packetRuleEngine.filterPackets(packets, entity);
            logger.info("Filtered to " + filteredPackets.size() + " packets based on business rules");

            // Update orderItem[0].instance.filterPackets with the filtered packets
            if (resultDb.getOrderItem() != null && !resultDb.getOrderItem().isEmpty()) {
                OrderItemEntity orderItem = resultDb.getOrderItem().get(0);

                // Create or get instance node
                JsonNode instanceNode = orderItem.getInstance();
                if (instanceNode == null) {
                    instanceNode = JsonNodeFactory.instance.objectNode();
                }

                // Create filterPackets node with filtered packets
                JsonNode filterPacketsNode = packetRuleEngine.createFilterPacketsNode(filteredPackets);

                // Update instance node with filterPackets
                ((ObjectNode) instanceNode).set("filterPackets", filterPacketsNode);

                // Update order item instance
                orderItem.setInstance(instanceNode);

                logger.info("Updated orderItem[0].instance.filterPackets with " + filteredPackets.size() + " packets");
            }

            return filteredPackets;
        } catch (Exception e) {
            logger.error("Error checking packets from order item: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    private Packet checkPacketFromOrderItem(OrderEntity entity, OrderEntity resultDb, String token){
        Packet packetResponse=null;
        if(entity.getOrderItem()!=null && entity.getOrderItem().size()>0 && entity.getOrderItem().get(0).getInsured_item()!=null && !entity.getOrderItem().get(0).getInsured_item().has("insurance_holders")) {
            if ((entity.getOrderItem().get(ORDER_ITEM).getInsured_item().has("tipologiaTitoloAbitazione") &&
                    entity.getOrderItem().get(ORDER_ITEM).getInsured_item().has("tipologiaUsoAbitazione") &&
                    entity.getOrderItem().get(ORDER_ITEM).getInsured_item().has("tipologiaCostruttivaAbitazione"))) {
                String titolo = entity.getOrderItem().get(ORDER_ITEM).getInsured_item().get("tipologiaTitoloAbitazione").asText();
                String uso = entity.getOrderItem().get(ORDER_ITEM).getInsured_item().get("tipologiaUsoAbitazione").asText();
                String tipologiaCostruttivaAbitazione = entity.getOrderItem().get(ORDER_ITEM).getInsured_item().get("tipologiaCostruttivaAbitazione").asText();
                packetResponse = invokePacketByTipologia(new TipologiaRequest(new TipologiaDtoRequest(titolo, uso, tipologiaCostruttivaAbitazione)), token);
                resultDb.setPacketId(packetResponse.packetResponse().getData().getId());
            }
        }
        if(packetResponse==null) {
            packetResponse = invokePacketByPacketId(Long.valueOf(resultDb.getPacketId()), token);
        }
        return packetResponse;
    }

    @Transactional
    @WithSpan("OrderService.checkEstimateKind")
    public OrderEntity checkEstimateKind(@SpanAttribute("arg.id") String order_code,
                                        @SpanAttribute("arg.xTenantLanguage") String tenantLanguage,
                                        @SpanAttribute("arg.entity") OrderEntity entity,
                                        String token) throws Exception {

        logger.info("Search order by orderCode: " + order_code);
        OrderEntity order = readOrderByOrderCodeEstimateKind(order_code);
        OrderEntity res = null;

        ProductResponse productResponse=invokeProductByProductId(Long.valueOf(order.getProductId()), token);
        String estimateKind = productResponse.getData().getConfiguration().getProperties().get("estimateKind").asText();

        logger.info("Choose estimate method checking estimateKind: " + estimateKind);
        switch(estimateKind) {
            case "internal":
            logger.info("Internal estimate");
            res = saveEstimate(order_code, tenantLanguage, entity, token);
            break;
            case "external":
            logger.info("External estimate");
            res = saveEstimateExternal(order_code, entity, productResponse, token);
            break;
        }

        return res;
    }

    @Transactional
    @WithSpan("OrderService.saveEstimate")
    public OrderEntity saveEstimate(
        @SpanAttribute("arg.id") String order_code,
        @SpanAttribute("arg.xTenantLanguage") String xTenantLanguage,
        @SpanAttribute("arg.entity") OrderEntity entity,
        String token) throws Exception {

        OrderEntity resultDb = repo.find("order_code", order_code).firstResult();
        if(resultDb == null){
            throw new EntityNotFoundException(" Entity order by order_code=%d not found, id ", order_code);
        }
        ProductResponse productResultDb = serviceClientProduct.getById(Long.valueOf(resultDb.getProductId()), token);
        AnagStatesEntity stateUpdating = stateRepo.find("state", STATE_ORDER_ESTIMATE).firstResult();
        resultDb.setAnagStates(stateUpdating);
        String ndg=jsonWebToken.getClaim("username");
        JsonNode customer=serviceClientCustomer.findByNdg(ndg);
        Integer idCustomer=customer.get("data").get("id").asInt();
        if(ndg.equalsIgnoreCase(tchUser) && resultDb.getCustomerId() == idCustomer){
            //utente non loggato
            resultDb.setAnonymousUserData(entity.getAnonymousUserData());
            //documentale
            ProductResponse productResponse=invokeProductByProductId(Long.valueOf(resultDb.getProductId()), token);
            CertificateRequestDto certificateReq = MapperOrder.OrderToCertificateReq(resultDb, productResponse, entity, YOLO);
            CertificateResponseDto certificate = serviceClientDocument.generateCertificate(certificateReq);
            //invio e-mail
            CommunicationManagerDtoRequest communicationManagerDtoRequest = new CommunicationManagerDtoRequest();
            TemplateRequest templateRequest = new TemplateRequest();
            templateRequest.setOrderResponse(resultDb);
            templateRequest.setProductResponse(productResultDb);
            templateRequest.setCertificateResponseDto(certificate);
            if(entity.getAnonymousUserData()!=null && entity.getAnonymousUserData().has("primary_mail")) {
                templateRequest.setPrimary_mail(entity.getAnonymousUserData().get("primary_mail").asText());
            } else {
                templateRequest.setPrimary_mail(customer.get("data").get("primary_mail").asText());
            }
            TemplateResponseDto templateResponseDto = netHomeflix.generate(templateRequest);
            communicationManagerDtoRequest.setMessage(templateResponseDto.getMessage());
            communicationManagerDtoRequest.setOptions(templateResponseDto.getOptions());
            communicationManagerDtoRequest.setAttachments(templateResponseDto.getAttachment());
            serviceClientComunication.sendEmail(communicationManagerDtoRequest);
        }

        OrderEntity orderUpdated = repo.getEntityManager().merge(resultDb);

        orderHistoryService.createHistoryForOrderPut(orderUpdated, null);
        return orderUpdated;
    }


    @Transactional
    @WithSpan("OrderService.saveEstimate")
    public OrderEntity saveEstimateExternal(
            @SpanAttribute("arg.id") String order_code,
            @SpanAttribute("arg.entity") OrderEntity entity,
            @SpanAttribute("arg.productResponse") ProductResponse productResponse,
            String token
            ) throws Exception {

        logger.info("Find orderEntity by orderCode: " + order_code);
        OrderEntity resultDb = repo.find("order_code", order_code).firstResult();
        if(resultDb == null){
            logger.info("Order with orderCode " + order_code + " not found");
            throw new EntityNotFoundException(" Entity order by order_code=%d not found, id ", order_code);
        }
        ProductResponse productResultDb = serviceClientProduct.getById(Long.valueOf(resultDb.getProductId()), token);
        AnagStatesEntity stateUpdating = stateRepo.find("state", STATE_ORDER_ESTIMATE).firstResult();
        resultDb.setAnagStates(stateUpdating);

        // documentale
        logger.info("Creating PGW request");
        PgwRequest pgRequest = pgwService.createPgwRequest(resultDb, token);
        logger.info("Call Providers Gateway estimateFull");
        PgwEstimateResponse pgwResponse;
        pgwResponse = pgwClient.estimateFull(pgRequest);

        // update instance
        JsonNode estimate = JsonNodeFactory.instance.objectNode()
        .put("estimateNumber", pgwResponse.getEstimateNumber())
        .put("estimateID", pgwResponse.getEstimateID())
        .set("providerResponse", pgwResponse.getProviderResponse().get("providerResponse"));

        resultDb.getOrderItem().forEach(orderItem->{
            if (orderItem.getInstance()==null){
                orderItem.setInstance(JsonNodeFactory.instance.objectNode());
            }
            ((ObjectNode) orderItem.getInstance()).set("estimate", estimate);
        });

        // Call upload iad-document
        if (pgwResponse.getBase64pdf() != null) {
            UploadCertificateRequestDto uploadCertificateRequestDto = new UploadCertificateRequestDto();
            uploadCertificateRequestDto.setFile(pgwResponse.getBase64pdf());
            uploadCertificateRequestDto.setNomeFile(order_code + ".pdf");
            logger.info("Upload estimate certificate calling iad-document service");
            JsonNode estimateCertificateResponse = serviceClientDocument.uploadCertificate(uploadCertificateRequestDto);

            CertificateResponseDto estimateCertificate = new CertificateResponseDto();
            estimateCertificate.setNomeFile(estimateCertificateResponse.get("nome_file").asText());
            estimateCertificate.setLink(estimateCertificateResponse.get("link").asText());
            estimateCertificate.setType(estimateCertificateResponse.get("type").asText());
            estimateCertificate.setFile(uploadCertificateRequestDto.getFile());

            //invio e-mail
            CommunicationManagerDtoRequest communicationManagerDtoRequest = new CommunicationManagerDtoRequest();
            TemplateRequest templateRequest = new TemplateRequest();
            templateRequest.setOrderResponse(resultDb);
            templateRequest.setCertificateResponseDto(estimateCertificate);

            JsonNode orderCustomerEmail = resultDb.getOrderItem().get(0).getInsured_item().get("customer").get("email");
            JsonNode customerEmail = pgRequest.getData().getCustomer().get("data").get("primary_mail");

            JsonNode email = orderCustomerEmail != null ? orderCustomerEmail : customerEmail;
            templateRequest.setProductResponse(productResultDb);
            templateRequest.setPrimary_mail(email.asText());
            templateRequest.setPrimary_mail(entity.getAnonymousUserData().get("primary_mail").asText());
            TemplateResponseDto templateResponseDto = netHomeflix.generate(templateRequest);
            communicationManagerDtoRequest.setMessage(templateResponseDto.getMessage());
            communicationManagerDtoRequest.setOptions(templateResponseDto.getOptions());
            communicationManagerDtoRequest.setAttachments(templateResponseDto.getAttachment());
            logger.info("Send email to customer with email: " + templateRequest.getPrimary_mail());
            serviceClientComunication.sendEmail(communicationManagerDtoRequest);
        }


        logger.info("Update order with orderCode: " + order_code);
        OrderEntity orderUpdated = repo.getEntityManager().merge(resultDb);

        orderHistoryService.createHistoryForOrderPut(orderUpdated, null);
        return orderUpdated;
    }

    @Transactional
    @WithSpan("OrderService.saveEstimate")
    public CertificateResponseDto getEstimate(
            @SpanAttribute("arg.id") String order_code,
            String token) throws Exception {

        OrderEntity resultDb = repo.find("order_code", order_code).firstResult();
        if(resultDb == null){
            throw new EntityNotFoundException(" Entity order by order_code=%d not found, id ", order_code);
        }
        AnagStatesEntity stateUpdating = stateRepo.find("state", STATE_ORDER_ESTIMATE).firstResult();
        String ndg=jsonWebToken.getClaim("username");
        Integer idCustomer=serviceClientCustomer.findByNdg(ndg).get("data").get("id").asInt();
        //utente non loggato
        //documentale
        ProductResponse productResponse=invokeProductByProductId(Long.valueOf(resultDb.getProductId()), token);
        CertificateRequestDto certificateReq = MapperOrder.OrderToCertificateReq(resultDb, productResponse, null, YOLO);
        CertificateResponseDto certificate = serviceClientDocument.generateCertificate(certificateReq);
        return certificate;
    }
    @Transactional
    public Response preContractualEmail(String orderCode, String token) throws Exception {

        OrderEntity order = repo.find("order_code", orderCode).firstResult();
        if(order == null){
            throw new EntityNotFoundException(" Entity order by order_code=%d not found, id ", orderCode);
        }
        if(order.getOrderItem().get(0).getInstance()==null){
            order.getOrderItem().get(0).setInstance(JsonNodeFactory.instance.objectNode());
        }
        if(order.getOrderItem().get(0).getInstance().hasNonNull("precontractualEmailSent") &&
                order.getOrderItem().get(0).getInstance().get("precontractualEmailSent").asBoolean()){
            return Response.ok().build();
        }
        String ndg=jsonWebToken.getClaim("username");
        JsonNode customer=serviceClientCustomer.findByNdg(ndg);
        ProductResponse productResponse=invokeProductByProductId(Long.valueOf(order.getProductId()), token);
        JsonNode preContractualConfig=productResponse.getData().getConfiguration().getProperties().get("preContractualEmail");
        CommunicationManagerDtoRequest request=new CommunicationManagerDtoRequest();
        if(preContractualConfig.hasNonNull("cc") && !preContractualConfig.withArray("cc").isEmpty()){
            List<String> cc=new ArrayList<>();
            preContractualConfig.withArray("cc").forEach(m->{
                cc.add(m.asText());
            });
            request.getOptions().setCc(cc);
        }
        request.getOptions().setLanguage(order.getLanguage()!=null ? order.getLanguage() : preContractualConfig.get("language").asText());
        request.getOptions().setToMail(customer.get("data").get("primary_mail").asText());
        request.getOptions().setMessaggetype(preContractualConfig.get("messageType").asText());
        request.getOptions().setTemplatePlaceholder(getTemplatePlaceholders(preContractualConfig.withArray("placeholders"), customer, productResponse));
        request.setAttachments(getAttachments(preContractualConfig.withArray("attachments"), productResponse));
        request.getMessage().setKey(preContractualConfig.get("messageKey").asText());
        serviceClientComunication.sendEmail(request);
        ((ObjectNode)order.getOrderItem().get(0).getInstance()).put("precontractualEmailSent", true);
        repo.getEntityManager().merge(order);
        return Response.ok().build();
    }

    public Boolean checkTokenTI(){
        //check token appartenente a technical-users o intermediary-users
        List<JsonString> groupsJson=jsonWebToken.getClaim("cognito:groups");
        List<String> groups=new ArrayList<>();
        for (JsonString group : groupsJson) {
            groups.add(group.getString());
        }
        return (groups.contains(technicalUser) || groups.contains(intermediaryUser));
    }

    public Boolean checkTokenIntermediary(){
        //check token appartenente a technical-users o intermediary-users
        List<JsonString> groupsJson=jsonWebToken.getClaim("cognito:groups");
        List<String> groups=new ArrayList<>();
        if(groupsJson!=null) {
            for (JsonString group : groupsJson) {
                groups.add(group.getString());
            }
            return groups.contains(intermediaryUser);
        }
        return false;
    }
    private List<TemplatePlaceholder> getTemplatePlaceholders(ArrayNode placeholdersConfig, JsonNode customer, ProductResponse product){
        List<TemplatePlaceholder> placeholders=new ArrayList<>();
        placeholdersConfig.forEach(p->{
            TemplatePlaceholder placeholder=new TemplatePlaceholder();
            String placeholderString=p.asText();
            placeholder.setKey(placeholderString);
            if(placeholderString.equalsIgnoreCase("customer_name")){
                placeholder.setValue(customer.get("data").get("name").asText());
            } else if (placeholderString.equalsIgnoreCase("product_name")) {
                placeholder.setValue(product.getData().getDescription());
            }
            placeholders.add(placeholder);
        });
        return placeholders;
    }
    private List<Attachments> getAttachments(ArrayNode attachmentsConfig, ProductResponse product){
        List<Attachments> attachments=new ArrayList<>();
        attachmentsConfig.forEach(a -> {
            a.fields().forEachRemaining(f -> {
                try {
                    if (f.getKey().equalsIgnoreCase("informativeSet")) {
                        JsonNode infoSetNode = f.getValue();
                        attachments.add(AttachmentUtils.getAttachment(product.getData().getInformativeSet(),
                                infoSetNode.get("name").asText(), infoSetNode.get("contentType").asText()));
                    } else if (f.getKey().equalsIgnoreCase("privacyDocumentation")) {
                        JsonNode privacyNode = f.getValue();
                        attachments.add(AttachmentUtils.getAttachment(product.getData().getPrivacyDocumentationLink(),
                                privacyNode.get("name").asText(), privacyNode.get("contentType").asText()));
                    } else if (f.getKey().equalsIgnoreCase("attachment3_4_4Ter")) {
                        JsonNode attachmentNode = f.getValue();
                        attachments.add(AttachmentUtils.getAttachment(product.getData().getAttachment_3_4_4Ter(),
                                attachmentNode.get("name").asText(), attachmentNode.get("contentType").asText()));
                    }
                } catch (IOException e){
                    throw new RuntimeException(e);
                }
            });
        });
        return attachments;
    }

    public void assignSessionIdToOrder(OrderEntity entity, String sessionId){
        if(jwtUtilsService.extractGroupsCognito().contains("anonimo") || jwtUtilsService.extractGroupsCognito().contains("intermediary-anonimo")){
            logger.info("TOKEN AUTH ONED DENTRO ANONIMO: " + jsonWebToken.getRawToken()); //REMOVE
            if(sessionId == null || sessionId.isEmpty()){
                throw new RuntimeException("SessionId Header not found");
            }
            entity.setSession_id(sessionId);
        }
    }

    public void checkSessionId(OrderEntity entity, String sessionId){
        log.info("checkSessionId: OrderEntity {}", entity.getSession_id());
        log.info("checkSessionId: Session id {}", sessionId);
        if((jwtUtilsService.extractGroupsCognito().contains("anonimo") || jwtUtilsService.extractGroupsCognito().contains("intermediary-anonimo"))&& (entity.getSession_id() == null || !entity.getSession_id().equals(sessionId))){
            throw new RuntimeException("Session Id Mismatch");
        }
    }

    private AnswerResponse getAnswers(String orderCode, String token){
        AnswerResponse answers=null;
        try {
            answers=surveyClient.getByOrderCode(orderCode, token).readEntity(AnswerResponse.class);
        } catch (Exception e){
            logger.info("error while retrieving answers for order "+orderCode);
        }
        return answers;
    }
}
