package it.yolo.service.v3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import io.quarkus.logging.Log;
import it.yolo.client.response.client.packet.Product;
import it.yolo.common.TaxCodeUtils;
import it.yolo.entity.OrderEntity;
import it.yolo.exception.CompanyDataValidationException;
import it.yolo.exception.InsuredAgeNotMatchedException;
import it.yolo.service.client.ErrorService;
import lombok.Getter;
import lombok.Setter;
import org.jboss.logging.Logger;

import javax.enterprise.context.Dependent;
import javax.inject.Inject;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiPredicate;

@Dependent
public class OrderItemCheckService {

    private static final String UNDER_PREFIX = "under";
    private static final String OVER_PREFIX = "over";
    private static final String INSUREDS_TOTAL = "insureds_total";
    private static final String INSURANCE_HOLDERS = "insurance_holders";
    private static final String BIRTH_DATE = "birth_date";
    private static final String TAX_CODE = "taxcode";
    private static final String COMPANY_VALIDATION_ERROR = "COMPANY_VALIDATION_ERROR";

    @Inject
    Logger logger;

    @Inject
    ErrorService errorService;

    public void checkOrderItemsEntities(Product product, OrderEntity reqEntity, String token, LocalDateTime startDate) throws InsuredAgeNotMatchedException {
        validateInsuredItems(reqEntity, product, token, startDate, "deltaAge", (birthDate, age) -> isUnderAge(birthDate, age, startDate));
    }

    public void checkAge(Product product, String ndg, LocalDateTime todayDate, String birth) throws InsuredAgeNotMatchedException{
        ageValidation(product,todayDate,birth, (birthDate, age) -> isUnderAge(birthDate, age, todayDate),ndg);
    }


    private void validateInsuredItems(OrderEntity reqEntity, Product product, String token, LocalDateTime startDate, String ageSeparationProperty, BiPredicate<LocalDate, Long> ageCheck) throws InsuredAgeNotMatchedException {
        if (reqEntity.getOrderItem().isEmpty()) {
            return;
        }

        JsonNode insuredItem = reqEntity.getOrderItem().get(0).getInsured_item();
        if (!product.getConfiguration().getProperties().hasNonNull(ageSeparationProperty)) {
            logger.info("Age separation property not found in product configuration");
            return;
        }

        JsonNode ageSeparation = product.getConfiguration().getProperties().get(ageSeparationProperty);
        long age = ageSeparation.get("age").asLong();
        logger.info("Age separation to check under and over: " + age);
        String underOverString = ageSeparation.hasNonNull("underOver")
                ? ageSeparation.get("underOver").asText()
                : Long.toString(age);

        int underDeclared = getDeclaredValue(insuredItem, UNDER_PREFIX + underOverString);
        int overDeclared = getDeclaredValue(insuredItem, OVER_PREFIX + underOverString);
        int insuredsTotal = getDeclaredValue(insuredItem, INSUREDS_TOTAL);

        validateInsuredsTotal(underDeclared, overDeclared, insuredsTotal, token);

        ArrayNode insuranceHolders = insuredItem.withArray(INSURANCE_HOLDERS);
        if (insuranceHolders.isEmpty()) {
            logger.info("insurance_holders is empty");
            return;
        }

        InsuranceGroups groups = processInsuranceHolders(insuranceHolders, age, ageCheck);

        validateGroups(underDeclared, overDeclared, groups, underOverString, token);
    }

    private boolean isUnderAge(LocalDate birthDate, Long age, LocalDateTime startDate) {
        Log.infov("isUnderAge, age: {0}", age);
        return birthDate.plusYears(age).isAfter(startDate.toLocalDate());
    }

    private int getDeclaredValue(JsonNode node, String field) {
        return node.hasNonNull(field) ? node.get(field).asInt() : 0;
    }

    private void validateInsuredsTotal(int underDeclared, int overDeclared, int insuredsTotal, String token) throws InsuredAgeNotMatchedException {
        if (underDeclared + overDeclared != insuredsTotal) {
            logger.info(String.format("Mismatch insureds_total: expected %d, but found under=%d and over=%d",
                    insuredsTotal, underDeclared, overDeclared));
            String message = errorService.insuredsTotalMismatchError(token);
            String errorCode = ErrorService.INSURED_TOTAL_MISMATCH;
            throw new InsuredAgeNotMatchedException(errorCode,message);
        }
    }

    private void validateGroups(int underDeclared, int overDeclared, InsuranceGroups groups, String underOverString, String token) throws InsuredAgeNotMatchedException {
        if (groups.getUnder().size() != underDeclared || groups.getOver().size() != overDeclared) {
            String message = errorService.insuredsTotalMismatchError(token);
            String errorCode = ErrorService.INSURED_TOTAL_MISMATCH;
            throw new InsuredAgeNotMatchedException(errorCode,message);
        }
    }

    private InsuranceGroups processInsuranceHolders(ArrayNode insuranceHolders, long age, BiPredicate<LocalDate, Long> ageCheck) {
        InsuranceGroups groups = new InsuranceGroups();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");

        for (JsonNode holder : insuranceHolders) {
            LocalDate birthDate = extractBirthDate(holder, formatter);
            if (birthDate == null) continue;

            if (ageCheck.test(birthDate, age)) {
                groups.getUnder().add(holder);
            } else {
                groups.getOver().add(holder);
            }
        }
        return groups;
    }

    private void ageValidation(Product product, LocalDateTime startDate, String birth,BiPredicate<LocalDate, Long> ageCheck, String ndg) throws InsuredAgeNotMatchedException {
        Log.infov("Start age validation, ndg:{0}", ndg);
        Long minAge = product.getMinAge();
        Log.infov("Min age:{0}", minAge);
        Long maxAge = product.getMaxAge();
        Log.infov("Max age:{0}", maxAge);
        testAge(ndg,minAge,maxAge,ageCheck,birth);

    }

    private void testAge(String ndg, Long minAge, Long maxAge, BiPredicate<LocalDate, Long> ageCheck, String birth) {
        Log.infov("Test age start: birthDate string:{0}", birth);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate birthDate = LocalDate.parse(birth, formatter);

        if (ageCheck.test(birthDate, minAge)) {
            Log.info("under age");
            throw new InsuredAgeNotMatchedException("Customer under age: " + minAge,"400");
        } else if(!ageCheck.test(birthDate,maxAge)) {
            Log.info("over age");
            throw new InsuredAgeNotMatchedException("Customer over age: " + maxAge,"400");
        }
        Log.infov("Age ok");

    }
    private LocalDate extractBirthDateFromNdg(String ndg) {
        if (ndg != null && !ndg.isEmpty()) {
            Log.infov("ExtractingBirthDate from :{0}", ndg);
            return TaxCodeUtils.extractBirthDate(ndg);
        } else {
            logger.info("tax code is empty");
            return null;
        }
    }

    private LocalDate extractBirthDate(JsonNode holder, DateTimeFormatter formatter) {
        if (!holder.hasNonNull(BIRTH_DATE) || holder.get(BIRTH_DATE).asText().isEmpty()) {
            logger.info("birth_date is empty");
            if (holder.hasNonNull(TAX_CODE) && !holder.get(TAX_CODE).asText().isEmpty()) {
                return TaxCodeUtils.extractBirthDate(holder.get(TAX_CODE).asText());
            } else {
                logger.info("tax code is empty");
                return null;
            }
        } else {
            return LocalDate.parse(holder.get(BIRTH_DATE).asText(), formatter);
        }
    }

    @Getter
    @Setter
    private static class InsuranceGroups {
        private final ArrayNode under;
        private final ArrayNode over;

        public InsuranceGroups() {
            this.under = JsonNodeFactory.instance.arrayNode();
            this.over = JsonNodeFactory.instance.arrayNode();
        }

    }
    
    /**
     * Validates the company data in the company node inside insured_item within order_item,
     * comparing it with the customer data and throws an exception if there are errors.
     *
     * @param orderCode order code
     * @param companyData company data
     * @param customerData customer data
     * @param token authentication token
     */
    public void validateCompanyData(String orderCode, JsonNode companyData, JsonNode customerData, String token) {
        List<String> validationErrors = new ArrayList<>();

        if (companyData == null || customerData == null || !customerData.has("data")) {
            return;
        }

        JsonNode customerDataNode = customerData.get("data");

        // Fields mapping
        validateField(companyData, "vatNumber", customerDataNode, "vatcode", validationErrors);
        validateField(companyData, "companyName", customerDataNode, "company", validationErrors);
        //validateField(companyData, "taxCode", customerDataNode, "tax_code", validationErrors);
        //validateField(companyData, "registeredOfficeCertifiedEmail", customerDataNode, "certifiedEmail", validationErrors);
        //validateField(companyData, "registeredOfficeAddress", customerDataNode, "street", validationErrors);
        //validateField(companyData, "registeredOfficeCityName", customerDataNode, "city", validationErrors);
        //validateField(companyData, "registeredOfficePostalCode", customerDataNode, "zip_code", validationErrors);
        //validateField(companyData, "registeredOfficeState", customerDataNode, "countryCode", validationErrors);

        if (!validationErrors.isEmpty()) {
            String message = errorService.companyDataValidationError(token, COMPANY_VALIDATION_ERROR);
            logger.info(String.format("Errore nella validazione dei dati dell'utente business, per la polizza %s, utente id: %s, company: %s, per i seguenti campi: %s",
                    orderCode,
                    customerData.has("id") ? customerData.get("id").asText() : "N/A",
                    customerData.has("company") ? customerData.get("company").asText() : "N/A",
                    validationErrors));
            throw new CompanyDataValidationException(COMPANY_VALIDATION_ERROR, message);
        }
    }
    
    /**
     * Helper method to validate a single field at a time
     */
    private void validateField(JsonNode companyData, String companyField, 
                              JsonNode customerData, String customerField, 
                              List<String> validationErrors) {
        if (companyData.has(companyField) && customerData.has(customerField)) {
            String companyValue = companyData.get(companyField).asText().replaceAll("\\s+", "").toLowerCase();
            String customerValue = customerData.get(customerField).asText().replaceAll("\\s+", "").toLowerCase();
            
            if (!companyValue.equals(customerValue)) {
                validationErrors.add(customerField);
            }
        }
    }
}