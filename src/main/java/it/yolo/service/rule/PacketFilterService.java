package it.yolo.service.rule;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import it.yolo.client.response.client.product.ProductResponse;
import it.yolo.entity.OrderEntity;
import lombok.extern.slf4j.Slf4j;

import javax.enterprise.context.ApplicationScoped;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * Service for filtering packets based on filterPackets in order instance.
 *
 * This service provides functionality to filter packets in a product response
 * based on a list of packet IDs stored in the order instance.
 */
@ApplicationScoped
@Slf4j
public class PacketFilterService {

    /**
     * Filter packets in a product response based on filterPackets in order instance.
     *
     * @param entity The order entity containing the instance with filterPackets
     * @param productResponse The product response containing packets to filter
     * @return true if filtering was applied, false otherwise
     */
    public boolean filterProductPackets(OrderEntity entity, ProductResponse productResponse) {
        // Check if all required objects are available
        if (entity == null || entity.getOrderItem() == null || entity.getOrderItem().isEmpty() ||
            entity.getOrderItem().get(0).getInstance() == null ||
            !entity.getOrderItem().get(0).getInstance().has("filterPackets") ||
            productResponse == null || productResponse.getData() == null ||
            productResponse.getData().getPackets() == null) {
            return false;
        }

        log.info("FilterPackets available in instance node");
        JsonNode filterPackets = entity.getOrderItem().get(0).getInstance().get("filterPackets");

        // Extract packet IDs from filterPackets and filter the product packets
        if (filterPackets.isArray()) {
            // Create a set of packet IDs from filterPackets
            Set<Integer> filterPacketIds = StreamSupport.stream(filterPackets.spliterator(), false)
                .filter(packet -> packet.has("id"))
                .map(packet -> packet.get("id").asInt())
                .collect(Collectors.toSet());

            // Filter the packets in the product response
            ArrayNode originalPackets = productResponse.getData().getPackets();
            ArrayNode filteredPackets = JsonNodeFactory.instance.arrayNode();

            StreamSupport.stream(originalPackets.spliterator(), false)
                .filter(packet -> packet.has("id") && filterPacketIds.contains(packet.get("id").asInt()))
                .forEach(filteredPackets::add);

            // Replace the original packets with the filtered ones
            productResponse.getData().setPackets(filteredPackets);

            log.info("Applied packet filtering: kept {} packets out of {} based on filterPackets",
                    filteredPackets.size(), originalPackets.size());
            return true;
        }

        return false;
    }
}
