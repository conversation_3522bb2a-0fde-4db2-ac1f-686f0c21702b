package it.yolo.client.response.client.packet;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

/**
 * Represents packet data containing configuration, product and warranties information.
 * This class is used to model packets data for ceiling calculations.
 */
@Data
public class PacketData {
    private JsonNode configuration;
    private JsonNode product;
    private JsonNode warranties;
}
